{"exclude": ["./node_modules", "./build", "./public/build", "./cypress", "./cypress.config.ts", "**/*.d.ts"], "include": ["remix.env.d.ts", "**/*.ts", "**/*.tsx", "./app/**/*", "./cypress/**/*.ts"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["node"], "typeRoots": ["./node_modules/@types", "./app/types"], "skipLibCheck": true, "skipDefaultLibCheck": true, "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "module": "ES2022", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "target": "ES2022", "strict": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"~/*": ["./app/*"], "@/*": ["./app/*"]}, "allowSyntheticDefaultImports": true, "noEmit": true, "incremental": true, "sourceMap": true, "rootDirs": ["."], "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictBindCallApply": true, "strictPropertyInitialization": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "useUnknownInCatchVariables": true}}