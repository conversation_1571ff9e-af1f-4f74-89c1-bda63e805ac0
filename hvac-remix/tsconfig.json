{
  "exclude": [
    "./node_modules",
    "./build",
    "./public/build",
    "./cypress",
    "./cypress.config.ts",
    "**/*.d.ts"
  ],
  "include": [
    "remix.env.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "./app/**/*",
    "./cypress/**/*.ts"
  ],
  "compilerOptions": {
    /* Base Options */
    "esModuleInterop": true,
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "target": "ES2022",
    "module": "ES2022",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowSyntheticDefaultImports": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noImplicitReturns": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "useUnknownInCatchVariables": true,
    "forceConsistentCasingInFileNames": true,
    
    /* Paths */
    "baseUrl": ".",
    "paths": {
      "~/*": ["./app/*"],
      "@/*": ["./app/*"]
    },
    "typeRoots": [
      "./node_modules/@types",
      "./app/types"
    ],
    
    /* Type Checking */
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": true,
    "allowJs": true,
    "checkJs": false,
    "incremental": true,
    "sourceMap": true,
    
    /* Libraries */
    "lib": ["DOM", "DOM.Iterable", "ES2022"],
    "types": ["node"]
  }
}
