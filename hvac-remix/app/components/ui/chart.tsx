import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend as ChartLegend,
  BarElement,
  ArcElement,
} from 'chart.js';
import { Line as ChartLine, Bar as Chart<PERSON><PERSON>, Pie as Chart<PERSON>ie } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  ChartLegend
);

// Wrapper components to mimic recharts API
export const ResponsiveContainer: React.FC<{
  width?: string | number;
  height?: string | number;
  children: React.ReactNode
}> = ({ children, height = 300 }) => (
  <div style={{ width: '100%', height }}>
    {children}
  </div>
);

export const CartesianGrid: React.FC<{ strokeDasharray?: string }> = () => null;
export const XAxis: React.FC<{ dataKey?: string; [key: string]: any }> = () => null;
export const YAxis: React.FC<{ [key: string]: any }> = () => null;
export const Tooltip: React.FC<{ [key: string]: any }> = () => null;
export const Legend: React.FC<{ [key: string]: any }> = () => null;

// LineChart wrapper
export const LineChart: React.FC<{
  data: any[];
  children?: React.ReactNode;
  margin?: any;
  [key: string]: any;
}> = ({ data }) => {
  const chartData = {
    labels: data.map(item => item.date || item.name || item.label || item.x),
    datasets: [
      {
        label: 'Data',
        data: data.map(item => item.count || item.value || item.y),
        borderColor: '#007bff',
        backgroundColor: 'rgba(0, 123, 255, 0.1)',
        tension: 0.1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  return <ChartLine data={chartData} options={options} />;
};

// Line component (used inside LineChart)
export const Line: React.FC<{
  type?: string;
  dataKey?: string;
  stroke?: string;
  strokeWidth?: number;
  fill?: string;
  activeDot?: any;
  [key: string]: any;
}> = () => null;

// BarChart wrapper
export const BarChart: React.FC<{
  data: any[];
  children?: React.ReactNode;
  margin?: any;
  [key: string]: any;
}> = ({ data }) => {
  const chartData = {
    labels: data.map(item => item.date || item.name || item.label || item.x),
    datasets: [
      {
        label: 'Data',
        data: data.map(item => item.count || item.value || item.y),
        backgroundColor: 'rgba(0, 123, 255, 0.6)',
        borderColor: '#007bff',
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  return <ChartBar data={chartData} options={options} />;
};

// Bar component (used inside BarChart)
export const Bar: React.FC<{
  dataKey?: string;
  fill?: string;
  [key: string]: any;
}> = () => null;

// PieChart wrapper
export const PieChart: React.FC<{
  data: any[];
  children?: React.ReactNode;
  [key: string]: any;
}> = ({ data }) => {
  const chartData = {
    labels: data.map(item => item.name || item.label),
    datasets: [
      {
        data: data.map(item => item.value || item.count),
        backgroundColor: [
          '#FF6384',
          '#36A2EB',
          '#FFCE56',
          '#4BC0C0',
          '#9966FF',
          '#FF9F40',
        ],
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
    },
  };

  return <ChartPie data={chartData} options={options} />;
};

// Pie component (used inside PieChart)
export const Pie: React.FC<{
  data?: any[];
  dataKey?: string;
  cx?: string | number;
  cy?: string | number;
  outerRadius?: number;
  fill?: string;
  children?: React.ReactNode;
  [key: string]: any;
}> = () => null;

// Cell component (used inside Pie)
export const Cell: React.FC<{
  fill?: string;
  [key: string]: any;
}> = () => null;

// Additional components that might be used
export const AreaChart: React.FC<{
  data: any[];
  children?: React.ReactNode;
  margin?: any;
  [key: string]: any;
}> = ({ data }) => {
  const chartData = {
    labels: data.map(item => item.date || item.name || item.label || item.x),
    datasets: [
      {
        label: 'Data',
        data: data.map(item => item.count || item.value || item.y),
        borderColor: '#007bff',
        backgroundColor: 'rgba(0, 123, 255, 0.3)',
        fill: true,
        tension: 0.1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  return <ChartLine data={chartData} options={options} />;
};

export const Area: React.FC<{
  type?: string;
  dataKey?: string;
  stroke?: string;
  fill?: string;
  stackId?: string;
  [key: string]: any;
}> = () => null;

export const ReferenceLine: React.FC<{
  y?: number;
  x?: number;
  stroke?: string;
  strokeDasharray?: string;
  [key: string]: any;
}> = () => null;

export const ScatterChart: React.FC<{
  data: any[];
  children?: React.ReactNode;
  margin?: any;
  [key: string]: any;
}> = ({ data }) => {
  const chartData = {
    datasets: [
      {
        label: 'Scatter Dataset',
        data: data.map(item => ({
          x: item.x || item.date,
          y: item.y || item.value || item.count,
        })),
        backgroundColor: 'rgba(0, 123, 255, 0.6)',
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
    },
    scales: {
      x: {
        type: 'linear' as const,
        position: 'bottom' as const,
      },
      y: {
        beginAtZero: true,
      },
    },
  };

  return <div>Scatter Chart (Chart.js scatter not implemented yet)</div>;
};

export const Scatter: React.FC<{
  dataKey?: string;
  fill?: string;
  [key: string]: any;
}> = () => null;

export const ZAxis: React.FC<{
  dataKey?: string;
  range?: number[];
  [key: string]: any;
}> = () => null;

export const Sankey: React.FC<{
  data?: any;
  [key: string]: any;
}> = () => null;

export const Rectangle: React.FC<{
  [key: string]: any;
}> = () => null;

interface ChartData {
  date: string;
  count: number;
}

interface ChartProps {
  data: ChartData[];
  title: string;
}

export function ServiceTrendChart({ data, title }: ChartProps) {
  return (
    <div className="h-[300px] w-full">
      <h3 className="text-lg font-medium mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="date" />
          <YAxis />
          <Tooltip />
          <Line type="monotone" dataKey="count" stroke="#007bff" activeDot={{ r: 8 }} />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
}
