// resolver.js
import { URL, pathToFileURL } from 'url';
import path from 'path';

const baseURL = new URL('file://');
baseURL.pathname = `${process.cwd()}/`;

export function resolve(specifier, context, nextResolve) {
  // Check if the specifier is exactly '~' or starts with '~/'
  if (specifier === '~' || specifier.startsWith('~/')) {
    // Replace '~' or '~/' with the absolute path to the app directory
    const appPath = path.resolve(process.cwd(), 'app');
    const relativePath = specifier === '~' ? '' : specifier.slice(2);
    const fullPath = path.join(appPath, relativePath);

    // Convert to URL format
    const url = pathToFileURL(fullPath).href;

    return {
      url,
      shortCircuit: true
    };
  }

  // Let Node.js handle all other specifiers
  return nextResolve(specifier, context);
}
