/* global clients */

/**
 * HVAC CRM Service Worker
 *
 * This service worker provides offline capabilities for the HVAC CRM application.
 * It handles:
 * - Caching of static assets
 * - Caching of API responses
 * - Background sync for offline data
 * - Push notifications
 *
 * Version: 2.0.0
 */

// Cache version - increment this when making changes to caching strategy
const CACHE_VERSION = 'v2';

// Cache names
const STATIC_CACHE_NAME = `hvac-crm-static-${CACHE_VERSION}`;
const DYNAMIC_CACHE_NAME = `hvac-crm-dynamic-${CACHE_VERSION}`;
const API_CACHE_NAME = `hvac-crm-api-${CACHE_VERSION}`;
const IMAGES_CACHE_NAME = `hvac-crm-images-${CACHE_VERSION}`;
const FONTS_CACHE_NAME = `hvac-crm-fonts-${CACHE_VERSION}`;

// Assets to cache on install
const STATIC_ASSETS = [
  '/',
  '/offline',
  '/dashboard',
  '/login',
  '/health',
  '/build/manifest.json',
  '/build/root.css',
  '/build/root.js',
  '/build/entry.client.js',
  '/favicon.ico',
  '/logo.svg',
  '/logo192.png',
  '/logo512.png',
  '/manifest.json'
];

// Font files to cache
const FONT_FILES = [
  'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap',
  'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa1ZL7.woff2',
  'https://fonts.gstatic.com/s/inter/v12/UcC73FwrK3iLTeHuS_fvQtMwCp50KnMa2JL7SUc.woff2',
  'https://fonts.gstatic.com/s/poppins/v20/pxiEyp8kv8JHgFVrJJfecg.woff2',
  'https://fonts.gstatic.com/s/poppins/v20/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2'
];

// Image extensions to cache
const IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.svg', '.gif', '.webp', '.avif'];

// Routes that should not be cached
const UNCACHEABLE_ROUTES = [
  '/api/auth',
  '/api/login',
  '/api/logout',
  '/api/register',
  '/api/reset-password'
];

// Install event - cache static assets
self.addEventListener('install', (event) => {
  console.log('[Service Worker] Installing Service Worker...');

  // Skip waiting to activate immediately
  self.skipWaiting();

  event.waitUntil(
    Promise.all([
      // Cache static assets
      caches.open(STATIC_CACHE_NAME)
        .then((cache) => {
          console.log('[Service Worker] Precaching App Shell');
          return cache.addAll(STATIC_ASSETS);
        }),

      // Cache fonts
      caches.open(FONTS_CACHE_NAME)
        .then((cache) => {
          console.log('[Service Worker] Precaching Fonts');
          return cache.addAll(FONT_FILES);
        })
    ])
    .catch((error) => {
      console.error('[Service Worker] Precaching failed:', error);
    })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('[Service Worker] Activating Service Worker...');

  // Claim clients to take control immediately
  self.clients.claim();

  // Get all cache names that should be kept
  const currentCaches = [
    STATIC_CACHE_NAME,
    DYNAMIC_CACHE_NAME,
    API_CACHE_NAME,
    IMAGES_CACHE_NAME,
    FONTS_CACHE_NAME
  ];

  event.waitUntil(
    // Clean up old caches
    caches.keys()
      .then((keyList) => {
        return Promise.all(keyList.map((key) => {
          // If this cache name isn't in our currentCaches list, delete it
          if (!currentCaches.includes(key)) {
            console.log('[Service Worker] Removing old cache:', key);
            return caches.delete(key);
          }
        }));
      })
      .then(() => {
        console.log('[Service Worker] Claiming clients');
        return self.clients.claim();
      })
  );
});

// Fetch event - handle network requests
self.addEventListener('fetch', (event) => {
  const url = new URL(event.request.url);

  // Skip non-GET requests
  if (event.request.method !== 'GET') {
    return;
  }

  // Skip browser extension requests
  if (!(url.protocol === 'http:' || url.protocol === 'https:')) {
    return;
  }

  // Skip uncacheable routes
  if (UNCACHEABLE_ROUTES.some(route => url.pathname.startsWith(route))) {
    return;
  }

  // Handle API requests with stale-while-revalidate strategy
  if (url.pathname.startsWith('/api/')) {
    event.respondWith(handleApiRequest(event.request));
    return;
  }

  // Handle font requests with cache-first strategy
  if (
    url.hostname === 'fonts.googleapis.com' ||
    url.hostname === 'fonts.gstatic.com'
  ) {
    event.respondWith(handleFontRequest(event.request));
    return;
  }

  // Handle image requests with cache-first strategy
  const fileExtension = url.pathname.substring(url.pathname.lastIndexOf('.'));
  if (IMAGE_EXTENSIONS.includes(fileExtension)) {
    event.respondWith(handleImageRequest(event.request));
    return;
  }

  // Handle static assets with cache-first strategy
  if (
    url.pathname.startsWith('/build/') ||
    url.pathname.startsWith('/assets/') ||
    STATIC_ASSETS.includes(url.pathname)
  ) {
    event.respondWith(handleStaticAssetRequest(event.request));
    return;
  }

  // Handle navigation requests with network-first strategy
  if (event.request.mode === 'navigate') {
    event.respondWith(handleNavigationRequest(event.request));
    return;
  }

  // Default strategy: stale-while-revalidate
  event.respondWith(
    caches.match(event.request).then((cachedResponse) => {
      // Return cached response immediately if available
      const fetchPromise = fetch(event.request)
        .then((networkResponse) => {
          // Cache the new response
          if (networkResponse && networkResponse.ok) {
            const responseToCache = networkResponse.clone();
            caches.open(DYNAMIC_CACHE_NAME).then((cache) => {
              cache.put(event.request, responseToCache);
            });
          }
          return networkResponse;
        })
        .catch((error) => {
          console.error('[Service Worker] Fetch failed:', error);
          // If both cache and network fail, return a fallback
          return new Response('Network error', { status: 408, headers: { 'Content-Type': 'text/plain' } });
        });

      return cachedResponse || fetchPromise;
    })
  );
});

// Handle API requests with stale-while-revalidate strategy
async function handleApiRequest(request) {
  // Check cache first
  const cachedResponse = await caches.match(request);

  // Start network fetch
  const fetchPromise = fetch(request)
    .then(async (networkResponse) => {
      // Cache successful GET responses
      if (networkResponse.ok && request.method === 'GET') {
        const responseClone = networkResponse.clone();
        const cache = await caches.open(API_CACHE_NAME);
        await cache.put(request, responseClone);
      }

      return networkResponse;
    })
    .catch((error) => {
      console.error('[Service Worker] API fetch failed:', error);

      // If no cached response, return a JSON error response
      if (!cachedResponse) {
        return new Response(
          JSON.stringify({
            error: 'Network error',
            offline: true,
            message: 'You are offline and this data is not cached.'
          }),
          {
            status: 503,
            headers: { 'Content-Type': 'application/json' }
          }
        );
      }

      // If we have a cached response, we'll return it below
      return null;
    });

  // Return cached response immediately if available, otherwise wait for network
  return cachedResponse || fetchPromise;
}

// Handle font requests with cache-first strategy
async function handleFontRequest(request) {
  // Try cache first
  const cachedResponse = await caches.match(request);

  if (cachedResponse) {
    return cachedResponse;
  }

  // If not in cache, try network
  try {
    const response = await fetch(request);

    // Cache the response if valid
    if (response.ok) {
      const responseClone = response.clone();
      const cache = await caches.open(FONTS_CACHE_NAME);
      await cache.put(request, responseClone);
    }

    return response;
  } catch (error) {
    console.error('[Service Worker] Font fetch failed:', error);
    // Return a fallback font or empty response
    return new Response('', {
      status: 408,
      headers: { 'Content-Type': 'font/woff2' }
    });
  }
}

// Handle image requests with cache-first strategy
async function handleImageRequest(request) {
  // Try cache first
  const cachedResponse = await caches.match(request);

  if (cachedResponse) {
    return cachedResponse;
  }

  // If not in cache, try network
  try {
    const response = await fetch(request);

    // Cache the response if valid
    if (response.ok) {
      const responseClone = response.clone();
      const cache = await caches.open(IMAGES_CACHE_NAME);
      await cache.put(request, responseClone);
    }

    return response;
  } catch (error) {
    console.error('[Service Worker] Image fetch failed:', error);
    // Return a transparent 1x1 pixel SVG
    return new Response('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"></svg>', {
      headers: { 'Content-Type': 'image/svg+xml' }
    });
  }
}

// Handle static asset requests with cache-first strategy
async function handleStaticAssetRequest(request) {
  // Try cache first
  const cachedResponse = await caches.match(request);

  if (cachedResponse) {
    return cachedResponse;
  }

  // If not in cache, try network
  try {
    const response = await fetch(request);

    // Cache the response if valid
    if (response.ok) {
      const responseClone = response.clone();
      const cache = await caches.open(STATIC_CACHE_NAME);
      await cache.put(request, responseClone);
    }

    return response;
  } catch (error) {
    console.error('[Service Worker] Static asset fetch failed:', error);

    // Return appropriate fallback based on request type
    const url = new URL(request.url);
    if (url.pathname.endsWith('.css')) {
      return new Response('/* Offline fallback CSS */', { headers: { 'Content-Type': 'text/css' } });
    } else if (url.pathname.endsWith('.js')) {
      return new Response('console.log("Offline fallback JS");', { headers: { 'Content-Type': 'application/javascript' } });
    } else if (IMAGE_EXTENSIONS.some(ext => url.pathname.endsWith(ext))) {
      return new Response('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"></svg>', {
        headers: { 'Content-Type': 'image/svg+xml' }
      });
    }

    return new Response('Offline fallback content', { headers: { 'Content-Type': 'text/plain' } });
  }
}

// Handle navigation requests with network-first strategy
async function handleNavigationRequest(request) {
  // Try network first
  try {
    const response = await fetch(request);

    // Cache successful responses
    if (response.ok) {
      const responseClone = response.clone();
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      await cache.put(request, responseClone);
    }

    return response;
  } catch (error) {
    console.error('[Service Worker] Navigation fetch failed:', error);

    // If network fails, try to get from cache
    const cachedResponse = await caches.match(request);

    if (cachedResponse) {
      return cachedResponse;
    }

    // If not in cache, return the offline page
    const offlineResponse = await caches.match('/offline');
    return offlineResponse || new Response('You are offline and this page is not cached.', {
      status: 503,
      headers: { 'Content-Type': 'text/html' }
    });
  }
}

// Background sync event
self.addEventListener('sync', (event) => {
  console.log('[Service Worker] Background Sync:', event.tag);

  if (event.tag === 'sync-data') {
    event.waitUntil(syncData());
  }
});

// Push notification event
self.addEventListener('push', (event) => {
  console.log('[Service Worker] Push Received:', event);

  let data = { title: 'New Notification', body: 'Something happened in the app.' };

  if (event.data) {
    try {
      data = event.data.json();
    } catch (e) {
      data.body = event.data.text();
    }
  }

  const options = {
    body: data.body,
    icon: '/logo.svg',
    badge: '/logo.svg',
    data: data.data || {},
    actions: data.actions || [],
    vibrate: [100, 50, 100],
    tag: data.tag || 'default',
    renotify: data.renotify || false
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click event
self.addEventListener('notificationclick', (event) => {
  console.log('[Service Worker] Notification click:', event);

  event.notification.close();

  // Open the app and navigate to a specific page
  if (event.notification.data && event.notification.data.url) {
    event.waitUntil(
      clients.openWindow(event.notification.data.url)
    );
  } else {
    event.waitUntil(
      clients.openWindow('/')
    );
  }
});

// Sync data with the server
async function syncData() {
  // This function would be implemented to sync data from IndexedDB to the server
  console.log('[Service Worker] Syncing data...');

  // Notify clients that sync has started
  const clients = await self.clients.matchAll();
  clients.forEach(client => {
    client.postMessage({ type: 'SYNC_STARTED' });
  });

  try {
    // In a real implementation, this would get data from IndexedDB and send it to the server
    // For now, we'll just simulate a successful sync

    // Notify clients that sync was successful
    clients.forEach(client => {
      client.postMessage({ type: 'SYNC_SUCCESS' });
    });

    return true;
  } catch (error) {
    console.error('[Service Worker] Sync failed:', error);

    // Notify clients that sync failed
    clients.forEach(client => {
      client.postMessage({ type: 'SYNC_FAILED', error: error.message });
    });

    return false;
  }
}
