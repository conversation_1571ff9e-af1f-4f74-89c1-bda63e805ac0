package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"
	"gobackend-hvac-kratos/internal/langchain"
	"gobackend-hvac-kratos/internal/vectordb"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/tmc/langchaingo/embeddings"
	"github.com/tmc/langchaingo/llms/ollama"
)

// 🚀 Enhanced AI Service - Unified AI Pipeline with LangChain + Vector DB
// Combines LangChain Go, Chromem-Go, and JSON-Iterator for maximum performance

type EnhancedService struct {
	// Core services
	langchainService *langchain.Service
	vectordbService  *vectordb.ChromemService
	gemmaService     *GemmaGGUFService

	// Configuration
	config *conf.AI_Model
	log    *log.Helper

	// Performance tracking
	metrics *EnhancedAIMetrics

	// Caching and optimization
	responseCache map[string]*CachedResponse
	embedder      embeddings.Embedder
}

// EnhancedAIMetrics tracks comprehensive AI performance
type EnhancedAIMetrics struct {
	// Request metrics
	TotalRequests      int64
	SuccessfulRequests int64
	FailedRequests     int64

	// Performance metrics
	AvgResponseTime     float64
	AvgEmbeddingTime    float64
	AvgVectorSearchTime float64

	// Feature usage
	LangChainUsage    int64
	VectorSearchUsage int64
	CacheHits         int64
	CacheMisses       int64

	// Quality metrics
	AvgConfidenceScore  float64
	HighConfidenceCount int64
	LowConfidenceCount  int64
}

// CachedResponse represents a cached AI response
type CachedResponse struct {
	Response   string
	Confidence float64
	Timestamp  time.Time
	TTL        time.Duration
	Metadata   map[string]any
}

// EnhancedChatRequest extends basic chat with advanced features
type EnhancedChatRequest struct {
	*biz.ChatRequest
	UseVectorSearch    bool     `json:"use_vector_search"`
	SearchCollections  []string `json:"search_collections,omitempty"`
	MaxContextDocs     int      `json:"max_context_docs"`
	EnableCaching      bool     `json:"enable_caching"`
	RequiredConfidence float64  `json:"required_confidence"`
}

// EnhancedChatResponse provides comprehensive response data
type EnhancedChatResponse struct {
	*biz.ChatResponse
	ContextSources   []ContextSource `json:"context_sources,omitempty"`
	VectorSearchUsed bool            `json:"vector_search_used"`
	CacheUsed        bool            `json:"cache_used"`
	ProcessingTime   float64         `json:"processing_time_ms"`
	ConfidenceScore  float64         `json:"confidence_score"`
	Recommendations  []string        `json:"recommendations,omitempty"`
}

// ContextSource represents a source document used for context
type ContextSource struct {
	ID         string         `json:"id"`
	Content    string         `json:"content"`
	Similarity float64        `json:"similarity"`
	Collection string         `json:"collection"`
	Metadata   map[string]any `json:"metadata"`
}

// NewEnhancedService creates a new enhanced AI service
func NewEnhancedService(config *conf.AI_Model, logger log.Logger) (*EnhancedService, error) {
	helper := log.NewHelper(logger)
	helper.Info("🚀 Initializing Enhanced AI Service...")

	// Initialize embedder
	llm, err := ollama.New(
		ollama.WithModel("gemma:3b-instruct-q4_0"),
		ollama.WithServerURL(config.Endpoint),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize LLM for embedder: %w", err)
	}

	embedder, err := embeddings.NewEmbedder(llm)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize embedder: %w", err)
	}

	// Initialize LangChain service
	langchainService, err := langchain.NewService(config, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize LangChain service: %w", err)
	}

	// Initialize Vector DB service
	vectordbService, err := vectordb.NewChromemService(nil, embedder, logger)
	if err != nil {
		return nil, fmt.Errorf("failed to initialize Vector DB service: %w", err)
	}

	// Initialize Gemma service
	gemmaService := NewGemmaGGUFService(config, logger)

	service := &EnhancedService{
		langchainService: langchainService,
		vectordbService:  vectordbService,
		gemmaService:     gemmaService,
		config:           config,
		log:              helper,
		metrics:          &EnhancedAIMetrics{},
		responseCache:    make(map[string]*CachedResponse),
		embedder:         embedder,
	}

	helper.Info("✅ Enhanced AI Service initialized successfully!")
	return service, nil
}

// EnhancedChat processes chat requests with full AI pipeline
func (s *EnhancedService) EnhancedChat(ctx context.Context, req *EnhancedChatRequest) (*EnhancedChatResponse, error) {
	startTime := time.Now()
	s.log.WithContext(ctx).Infof("🤖 Processing enhanced chat: %s", req.Message)
	s.metrics.TotalRequests++

	// Check cache first
	if req.EnableCaching {
		if cached := s.getCachedResponse(req.Message); cached != nil {
			s.metrics.CacheHits++
			return s.buildEnhancedResponse(cached.Response, nil, true, time.Since(startTime), cached.Confidence), nil
		}
		s.metrics.CacheMisses++
	}

	var contextSources []ContextSource
	var response *biz.ChatResponse
	var err error

	// Use vector search for context if requested
	if req.UseVectorSearch {
		contextSources, err = s.performVectorSearch(ctx, req)
		if err != nil {
			s.log.WithContext(ctx).Warnf("Vector search failed: %v", err)
		} else {
			s.metrics.VectorSearchUsage++
			// Add context to request
			req.Context = s.extractContextFromSources(contextSources)
		}
	}

	// Process with LangChain for advanced reasoning
	if s.shouldUseLangChain(req.ChatRequest) {
		s.metrics.LangChainUsage++
		response, err = s.langchainService.Chat(ctx, req.ChatRequest)
	} else {
		// Fallback to direct Gemma
		response, err = s.gemmaService.Chat(ctx, req.ChatRequest)
	}

	if err != nil {
		s.metrics.FailedRequests++
		return nil, fmt.Errorf("enhanced chat failed: %w", err)
	}

	s.metrics.SuccessfulRequests++
	processingTime := time.Since(startTime)
	s.updateAvgResponseTime(processingTime.Seconds() * 1000) // Convert to milliseconds

	// Calculate confidence score
	confidence := s.calculateEnhancedConfidence(response.Response, contextSources)
	s.updateConfidenceMetrics(confidence)

	// Cache response if enabled
	if req.EnableCaching {
		s.cacheResponse(req.Message, response.Response, confidence)
	}

	// Generate recommendations
	recommendations := s.generateRecommendations(ctx, req, response, contextSources)

	enhancedResponse := &EnhancedChatResponse{
		ChatResponse:     response,
		ContextSources:   contextSources,
		VectorSearchUsed: req.UseVectorSearch && len(contextSources) > 0,
		CacheUsed:        false,
		ProcessingTime:   processingTime.Seconds() * 1000,
		ConfidenceScore:  confidence,
		Recommendations:  recommendations,
	}

	return enhancedResponse, nil
}

// IntelligentHVACAnalysis performs comprehensive HVAC issue analysis
func (s *EnhancedService) IntelligentHVACAnalysis(ctx context.Context, customerIssue, systemType string) (*langchain.HVACAnalysisResult, error) {
	s.log.WithContext(ctx).Info("🔧 Performing intelligent HVAC analysis...")

	// Search for similar issues in vector database
	searchOptions := vectordb.SearchOptions{
		TopK:      5,
		Threshold: 0.7,
	}

	similarIssues, err := s.vectordbService.SearchHVACKnowledge(ctx, customerIssue, searchOptions)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Vector search for HVAC issues failed: %v", err)
	}

	// Use LangChain for structured analysis
	result, err := s.langchainService.ProcessHVACIssue(ctx, customerIssue, systemType)
	if err != nil {
		return nil, fmt.Errorf("HVAC analysis failed: %w", err)
	}

	// Enhance result with similar cases
	if len(similarIssues) > 0 {
		result.Metadata["similar_cases"] = s.formatSimilarCases(similarIssues)
		result.Confidence = s.adjustConfidenceWithSimilarity(result.Confidence, similarIssues)
	}

	return result, nil
}

// SmartEmailProcessing processes emails with full AI pipeline
func (s *EnhancedService) SmartEmailProcessing(ctx context.Context, emailContent, sender string) (*langchain.EmailAnalysisResult, error) {
	s.log.WithContext(ctx).Info("📧 Processing email with smart AI pipeline...")

	// Search for similar emails
	searchOptions := vectordb.SearchOptions{
		TopK:      3,
		Threshold: 0.6,
	}

	similarEmails, err := s.vectordbService.SearchSimilarEmails(ctx, emailContent, searchOptions)
	if err != nil {
		s.log.WithContext(ctx).Warnf("Vector search for similar emails failed: %v", err)
	}

	// Process with LangChain
	result, err := s.langchainService.ProcessEmail(ctx, emailContent, sender)
	if err != nil {
		return nil, fmt.Errorf("email processing failed: %w", err)
	}

	// Enhance with similar email insights
	if len(similarEmails) > 0 {
		result.Metadata["similar_emails"] = s.formatSimilarEmails(similarEmails)
		result.RoutingInfo.SimilarCases = s.extractSimilarCaseIDs(similarEmails)
	}

	// Store processed email for future similarity searches
	emailDoc := vectordb.VectorDocument{
		ID:      fmt.Sprintf("email_%d", time.Now().Unix()),
		Content: emailContent,
		Metadata: map[string]any{
			"sender":       sender,
			"category":     result.Classification.Category,
			"priority":     result.Classification.Priority,
			"processed_at": time.Now(),
		},
	}

	go func() {
		if err := s.vectordbService.AddEmailDocument(context.Background(), emailDoc); err != nil {
			s.log.Errorf("Failed to store email document: %v", err)
		}
	}()

	return result, nil
}

// SemanticKnowledgeSearch performs semantic search across knowledge base
func (s *EnhancedService) SemanticKnowledgeSearch(ctx context.Context, query string, collections []string, topK int) (map[string][]vectordb.SearchResult, error) {
	s.log.WithContext(ctx).Infof("🔍 Performing semantic knowledge search: %s", query)

	searchOptions := vectordb.SearchOptions{
		TopK:      topK,
		Threshold: 0.5,
	}

	if len(collections) == 0 {
		// Search all collections
		return s.vectordbService.SearchAllCollections(ctx, query, searchOptions)
	}

	// Search specific collections
	results := make(map[string][]vectordb.SearchResult)
	for _, collection := range collections {
		switch collection {
		case "hvac":
			hvacResults, err := s.vectordbService.SearchHVACKnowledge(ctx, query, searchOptions)
			if err == nil {
				results["hvac"] = hvacResults
			}
		case "emails":
			emailResults, err := s.vectordbService.SearchSimilarEmails(ctx, query, searchOptions)
			if err == nil {
				results["emails"] = emailResults
			}
		case "knowledge":
			knowledgeResults, err := s.vectordbService.SearchKnowledgeBase(ctx, query, searchOptions)
			if err == nil {
				results["knowledge"] = knowledgeResults
			}
		}
	}

	return results, nil
}

// GetEnhancedMetrics returns comprehensive AI metrics
func (s *EnhancedService) GetEnhancedMetrics() *EnhancedAIMetrics {
	// Update with sub-service metrics
	langchainMetrics := s.langchainService.GetMetrics()
	vectordbMetrics := s.vectordbService.GetMetrics()

	s.metrics.LangChainUsage = langchainMetrics.ChainExecutions
	s.metrics.VectorSearchUsage = vectordbMetrics.QueriesExecuted
	s.metrics.AvgVectorSearchTime = vectordbMetrics.AvgQueryTime * 1000 // Convert to ms

	return s.metrics
}

// HealthCheck performs comprehensive health check
func (s *EnhancedService) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("🏥 Performing enhanced AI service health check...")

	// Check LangChain service
	if err := s.langchainService.HealthCheck(ctx); err != nil {
		return fmt.Errorf("langchain service unhealthy: %w", err)
	}

	// Check Vector DB service
	if err := s.vectordbService.HealthCheck(ctx); err != nil {
		return fmt.Errorf("vector db service unhealthy: %w", err)
	}

	// Check Gemma service
	if err := s.gemmaService.HealthCheck(ctx); err != nil {
		return fmt.Errorf("gemma service unhealthy: %w", err)
	}

	return nil
}

// Helper methods

func (s *EnhancedService) performVectorSearch(ctx context.Context, req *EnhancedChatRequest) ([]ContextSource, error) {
	searchOptions := vectordb.SearchOptions{
		TopK:      req.MaxContextDocs,
		Threshold: 0.6,
	}

	if req.MaxContextDocs == 0 {
		searchOptions.TopK = 3 // Default
	}

	allResults, err := s.vectordbService.SearchAllCollections(ctx, req.Message, searchOptions)
	if err != nil {
		return nil, err
	}

	var contextSources []ContextSource
	for collection, results := range allResults {
		for _, result := range results {
			contextSources = append(contextSources, ContextSource{
				ID:         result.ID,
				Content:    result.Content,
				Similarity: result.Similarity,
				Collection: collection,
				Metadata:   result.Metadata,
			})
		}
	}

	return contextSources, nil
}

func (s *EnhancedService) extractContextFromSources(sources []ContextSource) []string {
	var contexts []string
	for _, source := range sources {
		contexts = append(contexts, fmt.Sprintf("[%s] %s", source.Collection, source.Content))
	}
	return contexts
}

func (s *EnhancedService) shouldUseLangChain(req *biz.ChatRequest) bool {
	// Use LangChain for complex queries
	complexKeywords := []string{
		"analyze", "compare", "explain", "troubleshoot", "diagnose",
		"recommend", "suggest", "plan", "schedule", "estimate",
	}

	lowerMessage := strings.ToLower(req.Message)
	for _, keyword := range complexKeywords {
		if strings.Contains(lowerMessage, keyword) {
			return true
		}
	}

	return len(req.Context) > 0 // Use LangChain when context is provided
}

func (s *EnhancedService) calculateEnhancedConfidence(response string, sources []ContextSource) float64 {
	baseConfidence := 0.5

	// Increase confidence based on response length and detail
	if len(response) > 200 {
		baseConfidence += 0.2
	}

	// Increase confidence based on context sources
	if len(sources) > 0 {
		avgSimilarity := 0.0
		for _, source := range sources {
			avgSimilarity += source.Similarity
		}
		avgSimilarity /= float64(len(sources))
		baseConfidence += avgSimilarity * 0.3
	}

	// Cap at 0.95
	if baseConfidence > 0.95 {
		baseConfidence = 0.95
	}

	return baseConfidence
}

func (s *EnhancedService) updateAvgResponseTime(responseTime float64) {
	if s.metrics.AvgResponseTime == 0 {
		s.metrics.AvgResponseTime = responseTime
	} else {
		s.metrics.AvgResponseTime = (s.metrics.AvgResponseTime + responseTime) / 2
	}
}

func (s *EnhancedService) updateConfidenceMetrics(confidence float64) {
	s.metrics.AvgConfidenceScore = (s.metrics.AvgConfidenceScore + confidence) / 2

	if confidence >= 0.8 {
		s.metrics.HighConfidenceCount++
	} else if confidence < 0.5 {
		s.metrics.LowConfidenceCount++
	}
}

func (s *EnhancedService) getCachedResponse(query string) *CachedResponse {
	if cached, exists := s.responseCache[query]; exists {
		if time.Since(cached.Timestamp) < cached.TTL {
			return cached
		}
		// Remove expired cache
		delete(s.responseCache, query)
	}
	return nil
}

func (s *EnhancedService) cacheResponse(query, response string, confidence float64) {
	s.responseCache[query] = &CachedResponse{
		Response:   response,
		Confidence: confidence,
		Timestamp:  time.Now(),
		TTL:        time.Hour, // 1 hour TTL
		Metadata:   map[string]any{"cached_at": time.Now()},
	}
}

func (s *EnhancedService) buildEnhancedResponse(response string, sources []ContextSource, cached bool, processingTime time.Duration, confidence float64) *EnhancedChatResponse {
	return &EnhancedChatResponse{
		ChatResponse: &biz.ChatResponse{
			Response:   response,
			ModelUsed:  "enhanced-ai-pipeline",
			TokensUsed: int32(len(response) / 4),
		},
		ContextSources:   sources,
		VectorSearchUsed: len(sources) > 0,
		CacheUsed:        cached,
		ProcessingTime:   processingTime.Seconds() * 1000,
		ConfidenceScore:  confidence,
	}
}

func (s *EnhancedService) generateRecommendations(_ context.Context, req *EnhancedChatRequest, response *biz.ChatResponse, sources []ContextSource) []string {
	var recommendations []string

	// Recommend vector search if not used and confidence is low
	if !req.UseVectorSearch && s.calculateEnhancedConfidence(response.Response, sources) < 0.6 {
		recommendations = append(recommendations, "Consider enabling vector search for more accurate responses")
	}

	// Recommend specific collections based on query content
	if s.isHVACRelated(req.Message) && !contains(req.SearchCollections, "hvac") {
		recommendations = append(recommendations, "Search HVAC knowledge base for technical details")
	}

	// Recommend caching for frequently asked questions
	if !req.EnableCaching {
		recommendations = append(recommendations, "Enable caching for faster responses to similar queries")
	}

	return recommendations
}

func (s *EnhancedService) formatSimilarCases(results []vectordb.SearchResult) []map[string]any {
	var cases []map[string]any
	for _, result := range results {
		cases = append(cases, map[string]any{
			"id":         result.ID,
			"similarity": result.Similarity,
			"summary":    result.Content[:min(100, len(result.Content))],
			"metadata":   result.Metadata,
		})
	}
	return cases
}

func (s *EnhancedService) formatSimilarEmails(results []vectordb.SearchResult) []map[string]any {
	var emails []map[string]any
	for _, result := range results {
		emails = append(emails, map[string]any{
			"id":         result.ID,
			"similarity": result.Similarity,
			"preview":    result.Content[:min(150, len(result.Content))],
			"metadata":   result.Metadata,
		})
	}
	return emails
}

func (s *EnhancedService) extractSimilarCaseIDs(results []vectordb.SearchResult) []string {
	var ids []string
	for _, result := range results {
		ids = append(ids, result.ID)
	}
	return ids
}

func (s *EnhancedService) adjustConfidenceWithSimilarity(baseConfidence float64, similarResults []vectordb.SearchResult) float64 {
	if len(similarResults) == 0 {
		return baseConfidence
	}

	avgSimilarity := 0.0
	for _, result := range similarResults {
		avgSimilarity += result.Similarity
	}
	avgSimilarity /= float64(len(similarResults))

	// Boost confidence based on similarity
	boost := avgSimilarity * 0.2
	adjusted := baseConfidence + boost

	if adjusted > 0.95 {
		adjusted = 0.95
	}

	return adjusted
}

func (s *EnhancedService) isHVACRelated(message string) bool {
	hvacKeywords := []string{
		"hvac", "heating", "cooling", "air conditioning", "ventilation",
		"furnace", "boiler", "heat pump", "ductwork", "thermostat",
		"filter", "maintenance", "repair", "installation", "temperature",
	}

	lowerMessage := strings.ToLower(message)
	for _, keyword := range hvacKeywords {
		if strings.Contains(lowerMessage, keyword) {
			return true
		}
	}
	return false
}

// Utility functions - using modern Go patterns
func contains(slice []string, item string) bool {
	// Modern approach would use slices.Contains, but keeping simple for compatibility
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
