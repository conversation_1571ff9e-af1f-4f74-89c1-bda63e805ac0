package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"gobackend-hvac-kratos/internal/biz"
	"gobackend-hvac-kratos/internal/conf"

	"github.com/go-kratos/kratos/v2/log"
)

// GemmaGGUFService handles Gemma-3-4b-it-qat-q4_0-gguf model
type GemmaGGUFService struct {
	config     *conf.AI_Model
	httpClient *http.Client
	log        *log.Helper
	modelPath  string
	isLoaded   bool
}

// GemmaRequest represents a request to Gemma model
type GemmaRequest struct {
	Model       string         `json:"model"`
	Messages    []GemmaMessage `json:"messages"`
	Temperature float64        `json:"temperature,omitempty"`
	MaxTokens   int            `json:"max_tokens,omitempty"`
	Stream      bool           `json:"stream"`
	Options     map[string]any `json:"options,omitempty"`
}

// GemmaMessage represents a chat message
type GemmaMessage struct {
	Role    string `json:"role"` // "system", "user", "assistant"
	Content string `json:"content"`
}

// GemmaResponse represents response from Gemma model
type GemmaResponse struct {
	Model              string `json:"model"`
	Response           string `json:"response"`
	Done               bool   `json:"done"`
	Context            []int  `json:"context,omitempty"`
	TotalDuration      int64  `json:"total_duration"`
	LoadDuration       int64  `json:"load_duration"`
	PromptEvalCount    int    `json:"prompt_eval_count"`
	PromptEvalDuration int64  `json:"prompt_eval_duration"`
	EvalCount          int    `json:"eval_count"`
	EvalDuration       int64  `json:"eval_duration"`
}

// NewGemmaGGUFService creates a new Gemma GGUF service
func NewGemmaGGUFService(config *conf.AI_Model, logger log.Logger) *GemmaGGUFService {
	return &GemmaGGUFService{
		config: config,
		httpClient: &http.Client{
			Timeout: 120 * time.Second, // Longer timeout for AI inference
		},
		log:       log.NewHelper(logger),
		modelPath: "google/gemma-3-4b-it-qat-q4_0-gguf",
		isLoaded:  false,
	}
}

// LoadModel loads the Gemma model (if using Ollama)
func (s *GemmaGGUFService) LoadModel(ctx context.Context) error {
	s.log.WithContext(ctx).Info("Loading Gemma-3-4b-it-qat-q4_0-gguf model...")

	// For Ollama, we need to pull the model first
	pullRequest := map[string]any{
		"name":   "gemma:3b-instruct-q4_0",
		"stream": false,
	}

	err := s.makeOllamaRequest(ctx, "POST", "/api/pull", pullRequest, nil)
	if err != nil {
		return fmt.Errorf("failed to pull Gemma model: %w", err)
	}

	s.isLoaded = true
	s.log.WithContext(ctx).Info("Gemma model loaded successfully! 🚀")
	return nil
} // Chat processes a chat request with Gemma model
func (s *GemmaGGUFService) Chat(ctx context.Context, req *biz.ChatRequest) (*biz.ChatResponse, error) {
	s.log.WithContext(ctx).Infof("Processing chat with Gemma GGUF model")

	// Ensure model is loaded
	if !s.isLoaded {
		if err := s.LoadModel(ctx); err != nil {
			return nil, err
		}
	}

	// Prepare messages for Gemma
	messages := []GemmaMessage{
		{
			Role:    "system",
			Content: "You are a helpful HVAC assistant. Provide accurate and professional responses about heating, ventilation, and air conditioning systems.",
		},
		{
			Role:    "user",
			Content: req.Message,
		},
	}

	// Add context if provided
	if len(req.Context) > 0 {
		contextMsg := GemmaMessage{
			Role:    "system",
			Content: fmt.Sprintf("Additional context: %s", strings.Join(req.Context, "\n")),
		}
		messages = append([]GemmaMessage{messages[0], contextMsg}, messages[1:]...)
	}

	gemmaReq := GemmaRequest{
		Model:       "gemma:3b-instruct-q4_0",
		Messages:    messages,
		Temperature: 0.7,
		MaxTokens:   2048,
		Stream:      false,
		Options: map[string]any{
			"num_ctx": 4096,
			"top_k":   40,
			"top_p":   0.9,
		},
	}

	var response GemmaResponse
	err := s.makeOllamaRequest(ctx, "POST", "/api/chat", gemmaReq, &response)
	if err != nil {
		return nil, fmt.Errorf("failed to chat with Gemma: %w", err)
	}

	return &biz.ChatResponse{
		Response:   response.Response,
		ModelUsed:  "gemma-3-4b-it-qat-q4_0-gguf",
		TokensUsed: int32(response.EvalCount),
	}, nil
}

// Analyze processes content analysis with Gemma
func (s *GemmaGGUFService) Analyze(ctx context.Context, req *biz.AnalyzeRequest) (*biz.AnalyzeResponse, error) {
	s.log.WithContext(ctx).Infof("Analyzing content with Gemma GGUF model")

	// Create analysis prompt
	analysisPrompt := s.createAnalysisPrompt(req.AnalysisType, req.Content)

	chatReq := &biz.ChatRequest{
		Message: analysisPrompt,
		Model:   "gemma-3-4b-it-qat-q4_0-gguf",
	}

	response, err := s.Chat(ctx, chatReq)
	if err != nil {
		return nil, err
	}

	return &biz.AnalyzeResponse{
		Analysis:   response.Response,
		Confidence: float32(s.calculateConfidence(response.Response)),
		Metadata: map[string]string{
			"model":         "gemma-3-4b-it-qat-q4_0-gguf",
			"analysis_type": req.AnalysisType,
			"tokens_used":   fmt.Sprintf("%d", response.TokensUsed),
		},
	}, nil
} // makeOllamaRequest makes HTTP request to Ollama API
func (s *GemmaGGUFService) makeOllamaRequest(ctx context.Context, method, endpoint string, payload any, response any) error {
	var body io.Reader

	if payload != nil {
		jsonData, err := json.Marshal(payload)
		if err != nil {
			return fmt.Errorf("failed to marshal request: %w", err)
		}
		body = bytes.NewBuffer(jsonData)
	}

	url := s.config.Endpoint + endpoint
	req, err := http.NewRequestWithContext(ctx, method, url, body)
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("failed to make request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(bodyBytes))
	}

	if response != nil {
		if err := json.NewDecoder(resp.Body).Decode(response); err != nil {
			return fmt.Errorf("failed to decode response: %w", err)
		}
	}

	return nil
}

// createAnalysisPrompt creates specialized prompts for different analysis types
func (s *GemmaGGUFService) createAnalysisPrompt(analysisType, content string) string {
	prompts := map[string]string{
		"sentiment": `Analyze the sentiment of the following text. Classify it as positive, negative, or neutral, and explain your reasoning:

%s

Provide your analysis in this format:
Sentiment: [positive/negative/neutral]
Confidence: [0-100]%%
Reasoning: [brief explanation]`,

		"hvac_issue": `As an HVAC expert, analyze the following customer message to identify potential HVAC issues, urgency level, and recommended actions:

%s

Provide your analysis in this format:
Issue Type: [heating/cooling/ventilation/maintenance/other]
Urgency: [low/medium/high/emergency]
Recommended Action: [brief recommendation]
Estimated Cost Range: [if applicable]`,

		"email_classification": `Classify the following email content into one of these categories: service_request, complaint, inquiry, appointment, billing, emergency. Also determine the priority level:

%s

Provide your analysis in this format:
Category: [category]
Priority: [low/medium/high/urgent]
Summary: [brief summary]`,

		"general": `Analyze the following content and provide insights:

%s

Provide a comprehensive analysis including key points, themes, and recommendations.`,
	}

	template, exists := prompts[analysisType]
	if !exists {
		template = prompts["general"]
	}

	return fmt.Sprintf(template, content)
} // calculateConfidence estimates confidence based on response characteristics
func (s *GemmaGGUFService) calculateConfidence(response string) float64 {
	// Simple heuristic-based confidence calculation
	confidence := 0.5 // Base confidence

	// Longer responses tend to be more confident
	if len(response) > 100 {
		confidence += 0.2
	}

	// Responses with specific terms indicate higher confidence
	confidenceIndicators := []string{
		"definitely", "certainly", "clearly", "obviously",
		"specifically", "precisely", "exactly", "confirmed",
	}

	for _, indicator := range confidenceIndicators {
		if strings.Contains(strings.ToLower(response), indicator) {
			confidence += 0.1
			if confidence > 0.95 {
				break
			}
		}
	}

	// Cap confidence at 0.95
	if confidence > 0.95 {
		confidence = 0.95
	}

	return confidence
}

// HealthCheck checks if the Gemma model is available and responsive
func (s *GemmaGGUFService) HealthCheck(ctx context.Context) error {
	s.log.WithContext(ctx).Info("Performing Gemma GGUF health check")

	// Simple ping to check if Ollama is running
	err := s.makeOllamaRequest(ctx, "GET", "/api/tags", nil, nil)
	if err != nil {
		return fmt.Errorf("connection failed: %w", err)
	}

	return nil
}

// GetModelInfo returns information about the loaded model
func (s *GemmaGGUFService) GetModelInfo(ctx context.Context) (*biz.AIModel, error) {
	return &biz.AIModel{
		Name:      "gemma-3-4b-it-qat-q4_0-gguf",
		Type:      "chat",
		Available: s.isLoaded,
		Endpoint:  s.config.Endpoint,
	}, nil
}

// GenerateHVACResponse generates HVAC-specific responses
func (s *GemmaGGUFService) GenerateHVACResponse(ctx context.Context, customerIssue, systemType string) (string, error) {
	prompt := fmt.Sprintf(`As an expert HVAC technician, provide a professional response to this customer issue:

Customer Issue: %s
System Type: %s

Provide a helpful response that includes:
1. Acknowledgment of the issue
2. Possible causes
3. Immediate steps the customer can take
4. When to call for professional service
5. Estimated timeline for resolution

Keep the response professional, helpful, and reassuring.`, customerIssue, systemType)

	chatReq := &biz.ChatRequest{
		Message: prompt,
		Model:   "gemma-3-4b-it-qat-q4_0-gguf",
	}

	response, err := s.Chat(ctx, chatReq)
	if err != nil {
		return "", err
	}

	return response.Response, nil
}
