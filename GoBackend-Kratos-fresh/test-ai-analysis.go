package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
)

// OpenAI-compatible request structure
type ChatRequest struct {
	Model    string    `json:"model"`
	Messages []Message `json:"messages"`
	Stream   bool      `json:"stream"`
}

type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// OpenAI-compatible response structure
type ChatResponse struct {
	Choices []Choice `json:"choices"`
}

type Choice struct {
	Message Message `json:"message"`
}

func main() {
	fmt.Println("🤖 HVAC AI Analysis Test")
	fmt.Println("========================")

	// Test email content
	emailContent := `
Temat: PILNE - Awaria klimatyzacji w biurze
Od: <EMAIL>
Do: <EMAIL>

Dzień dobry,

Ma<PERSON> poważną awarię klimatyzacji w naszym biurze. System nie działa od wczoraj 
i temperatura w pomieszczeniach jest bardzo wysoka (około 30°C). 

Pracownicy skarżą się na dyskomfort i trudności w pracy. To bardzo pilne - 
potrzebujemy natychmiastowej naprawy.

Proszę o pilny kontakt i wysłanie technika.

Pozdrawiam,
Jan Kowalski
Kierownik Biura
`

	// Prepare AI analysis prompt
	prompt := fmt.Sprintf(`Przeanalizuj ten email HVAC i podaj analizę w formacie JSON:

%s

Odpowiedz w formacie JSON z następującymi polami:
{
  "sentiment": "positive/negative/neutral",
  "sentiment_score": -1.0 do 1.0,
  "priority": "low/medium/high/urgent",
  "category": "emergency/maintenance/installation/inquiry",
  "hvac_relevance": 0.0 do 1.0,
  "urgency_level": "low/medium/high/critical",
  "detected_equipment": ["lista sprzętu"],
  "action_items": ["lista działań"],
  "recommended_response_time": "czas odpowiedzi"
}`, emailContent)

	// Prepare request
	request := ChatRequest{
		Model: "gemma-3-4b-it-qat",
		Messages: []Message{
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Stream: false,
	}

	// Convert to JSON
	jsonData, err := json.Marshal(request)
	if err != nil {
		log.Fatalf("❌ Błąd tworzenia JSON: %v", err)
	}

	fmt.Println("📤 Wysyłanie zapytania do AI...")

	// Send request to AI service
	resp, err := http.Post(
		"http://*************:1234/v1/chat/completions",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		log.Fatalf("❌ Błąd wysyłania zapytania: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Fatalf("❌ Błąd AI service (status %d): %s", resp.StatusCode, string(body))
	}

	fmt.Println("✅ Otrzymano odpowiedź od AI")

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("❌ Błąd odczytu odpowiedzi: %v", err)
	}

	// Parse response
	var chatResp ChatResponse
	if err := json.Unmarshal(body, &chatResp); err != nil {
		log.Fatalf("❌ Błąd parsowania odpowiedzi: %v", err)
	}

	if len(chatResp.Choices) == 0 {
		log.Fatalf("❌ Brak odpowiedzi w response")
	}

	aiResponse := chatResp.Choices[0].Message.Content
	fmt.Println("\n🤖 Odpowiedź AI:")
	fmt.Println("================")
	fmt.Println(aiResponse)

	// Try to parse as JSON
	fmt.Println("\n🔍 Analiza struktury odpowiedzi...")
	
	// Find JSON in response
	start := -1
	end := -1
	braceCount := 0
	
	for i, char := range aiResponse {
		if char == '{' {
			if start == -1 {
				start = i
			}
			braceCount++
		} else if char == '}' {
			braceCount--
			if braceCount == 0 && start != -1 {
				end = i + 1
				break
			}
		}
	}

	if start != -1 && end != -1 {
		jsonStr := aiResponse[start:end]
		fmt.Println("📋 Znaleziony JSON:")
		fmt.Println(jsonStr)

		// Try to parse the JSON
		var analysis map[string]interface{}
		if err := json.Unmarshal([]byte(jsonStr), &analysis); err != nil {
			fmt.Printf("⚠️  Błąd parsowania JSON: %v\n", err)
		} else {
			fmt.Println("\n✅ Analiza email zakończona pomyślnie!")
			fmt.Println("📊 Wyniki analizy:")
			
			if sentiment, ok := analysis["sentiment"]; ok {
				fmt.Printf("  • Sentiment: %v\n", sentiment)
			}
			if priority, ok := analysis["priority"]; ok {
				fmt.Printf("  • Priorytet: %v\n", priority)
			}
			if category, ok := analysis["category"]; ok {
				fmt.Printf("  • Kategoria: %v\n", category)
			}
			if hvac, ok := analysis["hvac_relevance"]; ok {
				fmt.Printf("  • HVAC Relevance: %v\n", hvac)
			}
			if urgency, ok := analysis["urgency_level"]; ok {
				fmt.Printf("  • Poziom pilności: %v\n", urgency)
			}
			if equipment, ok := analysis["detected_equipment"]; ok {
				fmt.Printf("  • Wykryty sprzęt: %v\n", equipment)
			}
			if actions, ok := analysis["action_items"]; ok {
				fmt.Printf("  • Działania: %v\n", actions)
			}
		}
	} else {
		fmt.Println("⚠️  Nie znaleziono JSON w odpowiedzi")
	}

	fmt.Println("\n🎉 ======================================")
	fmt.Println("✅ TEST AI ANALYSIS ZAKOŃCZONY!")
	fmt.Println("======================================")
	fmt.Println("🤖 AI service działa poprawnie")
	fmt.Println("📧 Email został przeanalizowany")
	fmt.Println("🔍 Analiza sentiment i priorytetu działa")
	fmt.Println("🏠 Wykrywanie HVAC relevance działa")
	fmt.Println("")
	fmt.Println("🚀 Gotowe do integracji z systemem email!")
}
