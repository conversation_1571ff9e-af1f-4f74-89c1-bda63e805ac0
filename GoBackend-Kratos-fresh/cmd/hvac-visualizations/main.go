package main

import (
	"encoding/json"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// 🎨 HVAC-Specific Data Visualizations
// Advanced components for HVAC industry data presentation

type HVACVisualization struct {
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Data        interface{}            `json:"data"`
	Config      map[string]interface{} `json:"config"`
	UI          VisualizationUI        `json:"ui"`
	Metadata    VisualizationMeta      `json:"metadata"`
}

type VisualizationUI struct {
	Colors      []string               `json:"colors"`
	Icons       map[string]string      `json:"icons"`
	Formatting  map[string]interface{} `json:"formatting"`
	Interactive bool                   `json:"interactive"`
	Responsive  bool                   `json:"responsive"`
	Animations  bool                   `json:"animations"`
}

type VisualizationMeta struct {
	LastUpdated string `json:"last_updated"`
	DataSource  string `json:"data_source"`
	RefreshRate string `json:"refresh_rate"`
	Accuracy    string `json:"accuracy"`
	SampleSize  int    `json:"sample_size"`
	Confidence  string `json:"confidence"`
}

// Equipment Health Dashboard
type EquipmentHealthData struct {
	EquipmentID     int64   `json:"equipment_id"`
	Name            string  `json:"name"`
	Type            string  `json:"type"`
	Brand           string  `json:"brand"`
	Model           string  `json:"model"`
	HealthScore     int     `json:"health_score"`
	HealthLabel     string  `json:"health_label"`
	HealthColor     string  `json:"health_color"`
	Status          string  `json:"status"`
	Temperature     float64 `json:"temperature"`
	Pressure        float64 `json:"pressure"`
	Efficiency      float64 `json:"efficiency"`
	LastMaintenance string  `json:"last_maintenance"`
	NextMaintenance string  `json:"next_maintenance"`
	Alerts          []Alert `json:"alerts"`
	Trends          Trends  `json:"trends"`
}

type Alert struct {
	Level   string `json:"level"` // info, warning, error, critical
	Message string `json:"message"`
	Icon    string `json:"icon"`
	Color   string `json:"color"`
	Time    string `json:"time"`
}

type Trends struct {
	Temperature []DataPoint `json:"temperature"`
	Pressure    []DataPoint `json:"pressure"`
	Efficiency  []DataPoint `json:"efficiency"`
}

type DataPoint struct {
	Time  string  `json:"time"`
	Value float64 `json:"value"`
}

// Service Performance Analytics
type ServicePerformanceData struct {
	TechnicianID         int64              `json:"technician_id"`
	Name                 string             `json:"name"`
	JobsCompleted        int                `json:"jobs_completed"`
	AvgResponseTime      string             `json:"avg_response_time"`
	CustomerSatisfaction float64            `json:"customer_satisfaction"`
	EfficiencyScore      int                `json:"efficiency_score"`
	Revenue              float64            `json:"revenue"`
	FormattedRevenue     string             `json:"formatted_revenue"`
	Specializations      []string           `json:"specializations"`
	Performance          PerformanceMetrics `json:"performance"`
}

type PerformanceMetrics struct {
	OnTimeRate         float64 `json:"on_time_rate"`
	FirstCallFix       float64 `json:"first_call_fix"`
	SafetyScore        int     `json:"safety_score"`
	TrainingHours      int     `json:"training_hours"`
	CertificationLevel string  `json:"certification_level"`
}

// Customer Journey Visualization
type CustomerJourneyData struct {
	CustomerID   int64                   `json:"customer_id"`
	Name         string                  `json:"name"`
	JourneyStage string                  `json:"journey_stage"`
	Touchpoints  []Touchpoint            `json:"touchpoints"`
	Satisfaction []SatisfactionDataPoint `json:"satisfaction"`
	Revenue      []RevenueDataPoint      `json:"revenue"`
	Predictions  CustomerPredictions     `json:"predictions"`
}

type Touchpoint struct {
	Date        string `json:"date"`
	Type        string `json:"type"` // call, email, visit, service
	Description string `json:"description"`
	Outcome     string `json:"outcome"`
	Icon        string `json:"icon"`
	Color       string `json:"color"`
}

type SatisfactionDataPoint struct {
	Date  string  `json:"date"`
	Score float64 `json:"score"`
	Event string  `json:"event"`
}

type RevenueDataPoint struct {
	Date   string  `json:"date"`
	Amount float64 `json:"amount"`
	Type   string  `json:"type"` // service, maintenance, installation
}

type CustomerPredictions struct {
	NextServiceDate    string   `json:"next_service_date"`
	ChurnRisk          string   `json:"churn_risk"`
	LifetimeValue      float64  `json:"lifetime_value"`
	RecommendedActions []string `json:"recommended_actions"`
}

func main() {
	log.Println("🎨 Starting HVAC Visualizations Server...")
	log.Println("🔧 Specialized HVAC Industry Data Presentation")

	router := mux.NewRouter()

	// HVAC-specific visualization endpoints
	router.HandleFunc("/api/visualizations/equipment-health", handleEquipmentHealth).Methods("GET")
	router.HandleFunc("/api/visualizations/service-performance", handleServicePerformance).Methods("GET")
	router.HandleFunc("/api/visualizations/customer-journey", handleCustomerJourney).Methods("GET")
	router.HandleFunc("/api/visualizations/temperature-map", handleTemperatureMap).Methods("GET")
	router.HandleFunc("/api/visualizations/efficiency-trends", handleEfficiencyTrends).Methods("GET")
	router.HandleFunc("/api/visualizations/predictive-maintenance", handlePredictiveMaintenance).Methods("GET")

	// Interactive dashboard
	router.HandleFunc("/", handleVisualizationDashboard).Methods("GET")
	router.HandleFunc("/visualizations", handleVisualizationDashboard).Methods("GET")

	// Health check
	router.HandleFunc("/health", handleHealth).Methods("GET")

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c.Handler(router)

	log.Println("🚀 HVAC Visualizations Server starting on :8081")
	log.Println("📊 Visualization Dashboard: http://localhost:8081/visualizations")
	log.Println("🔧 Equipment Health: http://localhost:8081/api/visualizations/equipment-health")
	log.Println("👨‍🔧 Service Performance: http://localhost:8081/api/visualizations/service-performance")
	log.Println("🛤️  Customer Journey: http://localhost:8081/api/visualizations/customer-journey")
	log.Println("🏥 Health: http://localhost:8081/health")

	if err := http.ListenAndServe(":8081", handler); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	response := map[string]interface{}{
		"status":  "healthy",
		"service": "hvac-visualizations",
		"version": "1.0.0",
		"time":    time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleEquipmentHealth(w http.ResponseWriter, r *http.Request) {
	// Mock equipment health data
	equipmentData := []EquipmentHealthData{
		{
			EquipmentID:     1,
			Name:            "Klimatyzacja Biuro A",
			Type:            "Split AC",
			Brand:           "Daikin",
			Model:           "FTXS35K",
			HealthScore:     85,
			HealthLabel:     "Dobry",
			HealthColor:     "#28a745",
			Status:          "Sprawny",
			Temperature:     22.5,
			Pressure:        4.2,
			Efficiency:      87.3,
			LastMaintenance: "2024-02-15",
			NextMaintenance: "2024-08-15",
			Alerts: []Alert{
				{Level: "info", Message: "Filtr wymaga wymiany za 30 dni", Icon: "ℹ️", Color: "#17a2b8", Time: "2024-05-01"},
			},
			Trends: Trends{
				Temperature: []DataPoint{
					{Time: "2024-05-01", Value: 22.1},
					{Time: "2024-05-15", Value: 22.3},
					{Time: "2024-05-29", Value: 22.5},
				},
				Pressure: []DataPoint{
					{Time: "2024-05-01", Value: 4.1},
					{Time: "2024-05-15", Value: 4.15},
					{Time: "2024-05-29", Value: 4.2},
				},
				Efficiency: []DataPoint{
					{Time: "2024-05-01", Value: 89.1},
					{Time: "2024-05-15", Value: 88.2},
					{Time: "2024-05-29", Value: 87.3},
				},
			},
		},
		{
			EquipmentID:     2,
			Name:            "Pompa Ciepła Dom",
			Type:            "Heat Pump",
			Brand:           "Mitsubishi",
			Model:           "PUHZ-SHW80VHA",
			HealthScore:     25,
			HealthLabel:     "Krytyczny",
			HealthColor:     "#dc3545",
			Status:          "Awaria",
			Temperature:     15.2,
			Pressure:        2.1,
			Efficiency:      45.8,
			LastMaintenance: "2024-01-10",
			NextMaintenance: "Pilne",
			Alerts: []Alert{
				{Level: "critical", Message: "Awaria kompresora - natychmiastowa naprawa", Icon: "🚨", Color: "#dc3545", Time: "2024-05-29"},
				{Level: "warning", Message: "Niskie ciśnienie w systemie", Icon: "⚠️", Color: "#ffc107", Time: "2024-05-28"},
			},
			Trends: Trends{
				Temperature: []DataPoint{
					{Time: "2024-05-01", Value: 18.5},
					{Time: "2024-05-15", Value: 16.8},
					{Time: "2024-05-29", Value: 15.2},
				},
				Pressure: []DataPoint{
					{Time: "2024-05-01", Value: 3.8},
					{Time: "2024-05-15", Value: 2.9},
					{Time: "2024-05-29", Value: 2.1},
				},
				Efficiency: []DataPoint{
					{Time: "2024-05-01", Value: 78.2},
					{Time: "2024-05-15", Value: 62.1},
					{Time: "2024-05-29", Value: 45.8},
				},
			},
		},
	}

	visualization := HVACVisualization{
		Type:        "equipment_health_dashboard",
		Title:       "Stan Zdrowia Urządzeń HVAC",
		Description: "Kompleksowy monitoring stanu technicznego urządzeń z predykcyjną analityką",
		Data:        equipmentData,
		Config: map[string]interface{}{
			"refresh_interval": "30s",
			"alert_threshold":  70,
			"show_trends":      true,
			"show_predictions": true,
		},
		UI: VisualizationUI{
			Colors:      []string{"#28a745", "#ffc107", "#fd7e14", "#dc3545"},
			Icons:       map[string]string{"healthy": "✅", "warning": "⚠️", "critical": "🚨", "maintenance": "🔧"},
			Formatting:  map[string]interface{}{"temperature": "°C", "pressure": "bar", "efficiency": "%"},
			Interactive: true,
			Responsive:  true,
			Animations:  true,
		},
		Metadata: VisualizationMeta{
			LastUpdated: time.Now().Format("15:04:05"),
			DataSource:  "IoT Sensors + Manual Inspections",
			RefreshRate: "Real-time",
			Accuracy:    "±2%",
			SampleSize:  2,
			Confidence:  "95%",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(visualization)
}

func handleServicePerformance(w http.ResponseWriter, r *http.Request) {
	// Mock service performance data
	performanceData := []ServicePerformanceData{
		{
			TechnicianID:         1,
			Name:                 "Marek Kowalczyk",
			JobsCompleted:        42,
			AvgResponseTime:      "1.2h",
			CustomerSatisfaction: 4.8,
			EfficiencyScore:      92,
			Revenue:              15750.00,
			FormattedRevenue:     "15,750 PLN",
			Specializations:      []string{"Klimatyzacja", "Wentylacja", "Diagnostyka"},
			Performance: PerformanceMetrics{
				OnTimeRate:         95.2,
				FirstCallFix:       87.5,
				SafetyScore:        98,
				TrainingHours:      40,
				CertificationLevel: "Senior",
			},
		},
		{
			TechnicianID:         2,
			Name:                 "Piotr Nowak",
			JobsCompleted:        38,
			AvgResponseTime:      "1.5h",
			CustomerSatisfaction: 4.6,
			EfficiencyScore:      88,
			Revenue:              12300.00,
			FormattedRevenue:     "12,300 PLN",
			Specializations:      []string{"Pompy ciepła", "Ogrzewanie", "Serwis"},
			Performance: PerformanceMetrics{
				OnTimeRate:         91.3,
				FirstCallFix:       82.1,
				SafetyScore:        96,
				TrainingHours:      35,
				CertificationLevel: "Regular",
			},
		},
	}

	visualization := HVACVisualization{
		Type:        "service_performance_analytics",
		Title:       "Analityka Wydajności Serwisu",
		Description: "Kompleksowa analiza wydajności techników i jakości usług",
		Data:        performanceData,
		Config: map[string]interface{}{
			"comparison_period": "month",
			"benchmark_enabled": true,
			"kpi_targets": map[string]float64{
				"satisfaction": 4.5,
				"efficiency":   85.0,
				"on_time":      90.0,
			},
		},
		UI: VisualizationUI{
			Colors:      []string{"#28a745", "#17a2b8", "#ffc107", "#6f42c1"},
			Icons:       map[string]string{"technician": "👨‍🔧", "satisfaction": "⭐", "efficiency": "⚡", "revenue": "💰"},
			Formatting:  map[string]interface{}{"currency": "PLN", "percentage": "%", "time": "h"},
			Interactive: true,
			Responsive:  true,
			Animations:  true,
		},
		Metadata: VisualizationMeta{
			LastUpdated: time.Now().Format("15:04:05"),
			DataSource:  "CRM + Time Tracking + Customer Feedback",
			RefreshRate: "Daily",
			Accuracy:    "±5%",
			SampleSize:  2,
			Confidence:  "90%",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(visualization)
}

func handleCustomerJourney(w http.ResponseWriter, r *http.Request) {
	// Mock customer journey data
	journeyData := []CustomerJourneyData{
		{
			CustomerID:   1,
			Name:         "Jan Kowalski",
			JourneyStage: "loyal_customer",
			Touchpoints: []Touchpoint{
				{Date: "2024-01-15", Type: "call", Description: "Pierwsze zapytanie o klimatyzację", Outcome: "Umówiona wizyta", Icon: "📞", Color: "#17a2b8"},
				{Date: "2024-01-20", Type: "visit", Description: "Wizyta techniczna i wycena", Outcome: "Akceptacja oferty", Icon: "🏠", Color: "#28a745"},
				{Date: "2024-02-01", Type: "service", Description: "Montaż klimatyzacji", Outcome: "Instalacja zakończona", Icon: "🔧", Color: "#28a745"},
				{Date: "2024-05-15", Type: "service", Description: "Przegląd okresowy", Outcome: "Wszystko sprawne", Icon: "✅", Color: "#28a745"},
			},
			Satisfaction: []SatisfactionDataPoint{
				{Date: "2024-01-20", Score: 4.2, Event: "Pierwsza wizyta"},
				{Date: "2024-02-01", Score: 4.8, Event: "Montaż"},
				{Date: "2024-05-15", Score: 4.9, Event: "Przegląd"},
			},
			Revenue: []RevenueDataPoint{
				{Date: "2024-02-01", Amount: 3500.00, Type: "installation"},
				{Date: "2024-05-15", Amount: 250.00, Type: "maintenance"},
			},
			Predictions: CustomerPredictions{
				NextServiceDate:    "2024-11-15",
				ChurnRisk:          "low",
				LifetimeValue:      12500.00,
				RecommendedActions: []string{"Zaproponuj rozszerzenie gwarancji", "Poinformuj o nowych usługach"},
			},
		},
	}

	visualization := HVACVisualization{
		Type:        "customer_journey_map",
		Title:       "Mapa Podróży Klienta",
		Description: "Wizualizacja pełnej ścieżki klienta z predykcjami i rekomendacjami",
		Data:        journeyData,
		Config: map[string]interface{}{
			"timeline_view":        true,
			"satisfaction_overlay": true,
			"revenue_tracking":     true,
			"predictions_enabled":  true,
		},
		UI: VisualizationUI{
			Colors:      []string{"#17a2b8", "#28a745", "#ffc107", "#6f42c1"},
			Icons:       map[string]string{"call": "📞", "visit": "🏠", "service": "🔧", "email": "📧"},
			Formatting:  map[string]interface{}{"currency": "PLN", "date": "DD.MM.YYYY"},
			Interactive: true,
			Responsive:  true,
			Animations:  true,
		},
		Metadata: VisualizationMeta{
			LastUpdated: time.Now().Format("15:04:05"),
			DataSource:  "CRM + Customer Feedback + Predictive Analytics",
			RefreshRate: "Real-time",
			Accuracy:    "±10%",
			SampleSize:  1,
			Confidence:  "85%",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(visualization)
}

func handleTemperatureMap(w http.ResponseWriter, r *http.Request) {
	// Mock temperature map data
	temperatureData := map[string]interface{}{
		"locations": []map[string]interface{}{
			{
				"id":          1,
				"name":        "Biuro A - Mokotów",
				"address":     "ul. Testowa 1, Warszawa",
				"coordinates": map[string]float64{"lat": 52.1672, "lng": 20.9679},
				"temperature": 22.5,
				"target_temp": 22.0,
				"status":      "optimal",
				"color":       "#28a745",
				"equipment":   "Daikin FTXS35K",
			},
			{
				"id":          2,
				"name":        "Dom - Nowa Huta",
				"address":     "ul. Przykładowa 2, Kraków",
				"coordinates": map[string]float64{"lat": 50.0775, "lng": 20.0532},
				"temperature": 15.2,
				"target_temp": 20.0,
				"status":      "critical",
				"color":       "#dc3545",
				"equipment":   "Mitsubishi PUHZ-SHW80VHA",
			},
		},
		"heatmap_data": []map[string]interface{}{
			{"lat": 52.1672, "lng": 20.9679, "intensity": 0.8},
			{"lat": 50.0775, "lng": 20.0532, "intensity": 0.3},
		},
	}

	visualization := HVACVisualization{
		Type:        "temperature_heatmap",
		Title:       "Mapa Temperatur",
		Description: "Geograficzna wizualizacja temperatur z wszystkich lokalizacji",
		Data:        temperatureData,
		Config: map[string]interface{}{
			"map_type":        "satellite",
			"cluster_markers": true,
			"show_heatmap":    true,
			"auto_zoom":       true,
		},
		UI: VisualizationUI{
			Colors:      []string{"#0066cc", "#28a745", "#ffc107", "#dc3545"},
			Icons:       map[string]string{"optimal": "✅", "warning": "⚠️", "critical": "🚨"},
			Formatting:  map[string]interface{}{"temperature": "°C"},
			Interactive: true,
			Responsive:  true,
			Animations:  true,
		},
		Metadata: VisualizationMeta{
			LastUpdated: time.Now().Format("15:04:05"),
			DataSource:  "IoT Temperature Sensors",
			RefreshRate: "5 minutes",
			Accuracy:    "±0.5°C",
			SampleSize:  2,
			Confidence:  "99%",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(visualization)
}

func handleEfficiencyTrends(w http.ResponseWriter, r *http.Request) {
	// Mock efficiency trends data
	trendsData := map[string]interface{}{
		"overall_efficiency": []map[string]interface{}{
			{"date": "2024-01", "efficiency": 85.2, "target": 85.0},
			{"date": "2024-02", "efficiency": 87.1, "target": 85.0},
			{"date": "2024-03", "efficiency": 89.3, "target": 85.0},
			{"date": "2024-04", "efficiency": 88.7, "target": 85.0},
			{"date": "2024-05", "efficiency": 90.1, "target": 85.0},
		},
		"equipment_breakdown": []map[string]interface{}{
			{"equipment": "Klimatyzacja", "efficiency": 87.3, "change": "+2.1%"},
			{"equipment": "Pompy ciepła", "efficiency": 45.8, "change": "-15.2%"},
			{"equipment": "Wentylacja", "efficiency": 92.1, "change": "+1.8%"},
		},
		"predictions": map[string]interface{}{
			"next_month": 91.2,
			"confidence": 0.85,
			"factors":    []string{"Sezonowe zmiany", "Planowane konserwacje", "Nowe instalacje"},
		},
	}

	visualization := HVACVisualization{
		Type:        "efficiency_trends",
		Title:       "Trendy Efektywności",
		Description: "Analiza trendów efektywności energetycznej z predykcjami",
		Data:        trendsData,
		Config: map[string]interface{}{
			"show_predictions": true,
			"show_targets":     true,
			"period":           "monthly",
			"benchmark":        85.0,
		},
		UI: VisualizationUI{
			Colors:      []string{"#28a745", "#17a2b8", "#ffc107", "#6f42c1"},
			Icons:       map[string]string{"trend_up": "📈", "trend_down": "📉", "target": "🎯"},
			Formatting:  map[string]interface{}{"percentage": "%", "change": "%"},
			Interactive: true,
			Responsive:  true,
			Animations:  true,
		},
		Metadata: VisualizationMeta{
			LastUpdated: time.Now().Format("15:04:05"),
			DataSource:  "Energy Meters + Performance Analytics",
			RefreshRate: "Daily",
			Accuracy:    "±3%",
			SampleSize:  50,
			Confidence:  "92%",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(visualization)
}

func handlePredictiveMaintenance(w http.ResponseWriter, r *http.Request) {
	// Mock predictive maintenance data
	maintenanceData := map[string]interface{}{
		"equipment_predictions": []map[string]interface{}{
			{
				"equipment_id":       1,
				"name":               "Klimatyzacja Biuro A",
				"failure_risk":       15,
				"risk_level":         "low",
				"risk_color":         "#28a745",
				"predicted_failure":  "2024-12-15",
				"recommended_action": "Wymiana filtra za 30 dni",
				"confidence":         92,
				"factors":            []string{"Wiek urządzenia", "Historia awarii", "Warunki eksploatacji"},
			},
			{
				"equipment_id":       2,
				"name":               "Pompa Ciepła Dom",
				"failure_risk":       85,
				"risk_level":         "critical",
				"risk_color":         "#dc3545",
				"predicted_failure":  "2024-06-01",
				"recommended_action": "Natychmiastowa naprawa kompresora",
				"confidence":         96,
				"factors":            []string{"Awaria kompresora", "Niskie ciśnienie", "Spadek wydajności"},
			},
		},
		"maintenance_schedule": []map[string]interface{}{
			{"date": "2024-06-01", "equipment": "Pompa Ciepła Dom", "type": "emergency", "priority": "critical"},
			{"date": "2024-06-15", "equipment": "Klimatyzacja Biuro A", "type": "preventive", "priority": "normal"},
			{"date": "2024-07-01", "equipment": "Wentylacja Magazyn", "type": "routine", "priority": "low"},
		},
	}

	visualization := HVACVisualization{
		Type:        "predictive_maintenance",
		Title:       "Predykcyjna Konserwacja",
		Description: "AI-powered predykcja awarii i optymalizacja harmonogramów konserwacji",
		Data:        maintenanceData,
		Config: map[string]interface{}{
			"prediction_horizon":   "6_months",
			"ai_model":             "random_forest",
			"confidence_threshold": 80,
			"auto_scheduling":      true,
		},
		UI: VisualizationUI{
			Colors:      []string{"#28a745", "#ffc107", "#fd7e14", "#dc3545"},
			Icons:       map[string]string{"low_risk": "✅", "medium_risk": "⚠️", "high_risk": "🔥", "critical_risk": "🚨"},
			Formatting:  map[string]interface{}{"percentage": "%", "date": "DD.MM.YYYY"},
			Interactive: true,
			Responsive:  true,
			Animations:  true,
		},
		Metadata: VisualizationMeta{
			LastUpdated: time.Now().Format("15:04:05"),
			DataSource:  "IoT Sensors + ML Algorithms + Historical Data",
			RefreshRate: "Hourly",
			Accuracy:    "±8%",
			SampleSize:  100,
			Confidence:  "94%",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(visualization)
}

func handleVisualizationDashboard(w http.ResponseWriter, r *http.Request) {
	// Serve the HVAC visualizations dashboard HTML
	html := `<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 HVAC Visualizations Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .visualization-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            min-height: 400px;
        }

        .health-score {
            font-size: 2rem;
            font-weight: bold;
        }

        .health-good { color: #28a745; }
        .health-warning { color: #ffc107; }
        .health-critical { color: #dc3545; }

        .gauge-container {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 0 auto;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                <strong>HVAC Visualizations</strong>
            </a>
            <div class="d-flex align-items-center">
                <span class="text-light me-3">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="refreshAll()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Equipment Health Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card visualization-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>
                            Stan Zdrowia Urządzeń
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadEquipmentHealth()">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    <div class="card-body" id="equipment-health">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Ładowanie...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Service Performance & Customer Journey -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card visualization-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-user-cog me-2"></i>
                            Wydajność Serwisu
                        </h5>
                    </div>
                    <div class="card-body" id="service-performance">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card visualization-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-route me-2"></i>
                            Podróż Klienta
                        </h5>
                    </div>
                    <div class="card-body" id="customer-journey">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Temperature Map & Efficiency Trends -->
        <div class="row mb-4">
            <div class="col-lg-6 mb-4">
                <div class="card visualization-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-thermometer-half me-2"></i>
                            Mapa Temperatur
                        </h5>
                    </div>
                    <div class="card-body" id="temperature-map">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card visualization-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-area me-2"></i>
                            Trendy Efektywności
                        </h5>
                    </div>
                    <div class="card-body" id="efficiency-trends">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Predictive Maintenance -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card visualization-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-brain me-2"></i>
                            Predykcyjna Konserwacja
                        </h5>
                    </div>
                    <div class="card-body" id="predictive-maintenance">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString('pl-PL');
        }

        setInterval(updateTime, 1000);
        updateTime();

        // Load equipment health visualization
        async function loadEquipmentHealth() {
            try {
                const response = await fetch('/api/visualizations/equipment-health');
                const data = await response.json();

                const container = document.getElementById('equipment-health');
                if (data.data) {
                    container.innerHTML = data.data.map(equipment =>
                        '<div class="row mb-3">' +
                        '<div class="col-md-6">' +
                        '<div class="d-flex align-items-center">' +
                        '<div class="me-3">' +
                        '<div class="health-score health-' + (equipment.health_score > 70 ? 'good' : equipment.health_score > 40 ? 'warning' : 'critical') + '">' +
                        equipment.health_score + '</div>' +
                        '<small class="text-muted">Health Score</small>' +
                        '</div>' +
                        '<div>' +
                        '<h6 class="mb-1">' + equipment.name + '</h6>' +
                        '<p class="mb-1 text-muted">' + equipment.brand + ' ' + equipment.model + '</p>' +
                        '<small class="badge" style="background-color: ' + equipment.health_color + '">' + equipment.status + '</small>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '<div class="col-md-6">' +
                        '<div class="row text-center">' +
                        '<div class="col-4">' +
                        '<div class="fw-bold">' + equipment.temperature + '°C</div>' +
                        '<small class="text-muted">Temperatura</small>' +
                        '</div>' +
                        '<div class="col-4">' +
                        '<div class="fw-bold">' + equipment.pressure + ' bar</div>' +
                        '<small class="text-muted">Ciśnienie</small>' +
                        '</div>' +
                        '<div class="col-4">' +
                        '<div class="fw-bold">' + equipment.efficiency + '%</div>' +
                        '<small class="text-muted">Wydajność</small>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        (equipment.alerts.length > 0 ?
                        '<div class="row">' +
                        '<div class="col-12">' +
                        equipment.alerts.map(alert =>
                            '<div class="alert alert-' + (alert.level === 'critical' ? 'danger' : alert.level === 'warning' ? 'warning' : 'info') + ' alert-sm">' +
                            alert.icon + ' ' + alert.message +
                            '</div>'
                        ).join('') +
                        '</div>' +
                        '</div>' : '') +
                        '<hr>'
                    ).join('');
                }
            } catch (error) {
                console.error('Error loading equipment health:', error);
            }
        }

        // Load service performance
        async function loadServicePerformance() {
            try {
                const response = await fetch('/api/visualizations/service-performance');
                const data = await response.json();

                const container = document.getElementById('service-performance');
                if (data.data) {
                    container.innerHTML = data.data.map(tech =>
                        '<div class="mb-3">' +
                        '<div class="d-flex justify-content-between align-items-center mb-2">' +
                        '<h6 class="mb-0">👨‍🔧 ' + tech.name + '</h6>' +
                        '<span class="badge bg-primary">' + tech.efficiency_score + '%</span>' +
                        '</div>' +
                        '<div class="row text-center">' +
                        '<div class="col-3">' +
                        '<div class="fw-bold">' + tech.jobs_completed + '</div>' +
                        '<small class="text-muted">Zlecenia</small>' +
                        '</div>' +
                        '<div class="col-3">' +
                        '<div class="fw-bold">' + tech.customer_satisfaction + ' ⭐</div>' +
                        '<small class="text-muted">Ocena</small>' +
                        '</div>' +
                        '<div class="col-3">' +
                        '<div class="fw-bold">' + tech.avg_response_time + '</div>' +
                        '<small class="text-muted">Czas</small>' +
                        '</div>' +
                        '<div class="col-3">' +
                        '<div class="fw-bold">' + tech.formatted_revenue + '</div>' +
                        '<small class="text-muted">Przychód</small>' +
                        '</div>' +
                        '</div>' +
                        '<div class="progress mt-2" style="height: 6px;">' +
                        '<div class="progress-bar" style="width: ' + tech.performance.on_time_rate + '%"></div>' +
                        '</div>' +
                        '<small class="text-muted">Punktualność: ' + tech.performance.on_time_rate + '%</small>' +
                        '</div>'
                    ).join('');
                }
            } catch (error) {
                console.error('Error loading service performance:', error);
            }
        }

        // Load all visualizations
        function refreshAll() {
            loadEquipmentHealth();
            loadServicePerformance();
            // Add other visualization loaders here
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            refreshAll();

            // Auto-refresh every 30 seconds
            setInterval(refreshAll, 30000);
        });
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}
