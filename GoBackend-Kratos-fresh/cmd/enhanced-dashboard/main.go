package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/cors"
)

// 🎨 Enhanced HVAC CRM Dashboard with Human-Readable Data Presentation
// Focuses on clarity, context, and user experience

type EnhancedResponse struct {
	Data      interface{}            `json:"data"`
	Meta      ResponseMeta           `json:"meta"`
	UI        UIEnhancements         `json:"ui"`
	Context   map[string]interface{} `json:"context"`
	Timestamp string                 `json:"timestamp"`
}

type ResponseMeta struct {
	Total         int    `json:"total"`
	Page          int    `json:"page"`
	PerPage       int    `json:"per_page"`
	HasNext       bool   `json:"has_next"`
	HasPrev       bool   `json:"has_prev"`
	QueryTime     string `json:"query_time"`
	DataFreshness string `json:"data_freshness"`
}

type UIEnhancements struct {
	Formatting     map[string]interface{} `json:"formatting"`
	Actions        []QuickAction          `json:"actions"`
	Filters        []FilterOption         `json:"filters"`
	Sorting        []SortOption           `json:"sorting"`
	Grouping       []GroupOption          `json:"grouping"`
	Visualizations []VisualizationType    `json:"visualizations"`
}

type QuickAction struct {
	ID           string `json:"id"`
	Label        string `json:"label"`
	Icon         string `json:"icon"`
	Color        string `json:"color"`
	Endpoint     string `json:"endpoint"`
	Confirmation bool   `json:"confirmation"`
}

type FilterOption struct {
	Field   string      `json:"field"`
	Label   string      `json:"label"`
	Type    string      `json:"type"` // select, date, range, search
	Options []string    `json:"options,omitempty"`
	Default interface{} `json:"default,omitempty"`
}

type SortOption struct {
	Field     string `json:"field"`
	Label     string `json:"label"`
	Direction string `json:"direction"` // asc, desc
	Default   bool   `json:"default"`
}

type GroupOption struct {
	Field string `json:"field"`
	Label string `json:"label"`
}

type VisualizationType struct {
	Type   string                 `json:"type"` // chart, gauge, timeline, map
	Config map[string]interface{} `json:"config"`
}

// Enhanced Customer with rich metadata
type EnhancedCustomer struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	Phone     string    `json:"phone"`
	Address   string    `json:"address"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Enhanced fields
	DisplayName    string             `json:"display_name"`
	FormattedPhone string             `json:"formatted_phone"`
	RelativeTime   string             `json:"relative_time"`
	Status         CustomerStatus     `json:"status"`
	Priority       Priority           `json:"priority"`
	Tags           []string           `json:"tags"`
	Stats          CustomerStats      `json:"stats"`
	LastContact    *ContactInfo       `json:"last_contact"`
	NextService    *ServiceInfo       `json:"next_service"`
	Equipment      []EquipmentSummary `json:"equipment"`
	Satisfaction   SatisfactionScore  `json:"satisfaction"`
	Location       LocationInfo       `json:"location"`
}

type CustomerStatus struct {
	Code        string `json:"code"` // active, inactive, vip, new
	Label       string `json:"label"`
	Color       string `json:"color"`
	Icon        string `json:"icon"`
	Description string `json:"description"`
}

type Priority struct {
	Level      int    `json:"level"` // 1-5
	Label      string `json:"label"` // Low, Normal, High, Urgent, Critical
	Color      string `json:"color"`
	Icon       string `json:"icon"`
	BadgeClass string `json:"badge_class"`
}

type CustomerStats struct {
	TotalJobs      int     `json:"total_jobs"`
	CompletedJobs  int     `json:"completed_jobs"`
	PendingJobs    int     `json:"pending_jobs"`
	TotalSpent     float64 `json:"total_spent"`
	FormattedSpent string  `json:"formatted_spent"`
	AvgJobValue    float64 `json:"avg_job_value"`
	FormattedAvg   string  `json:"formatted_avg"`
	LastJobDate    string  `json:"last_job_date"`
	CustomerSince  string  `json:"customer_since"`
}

type ContactInfo struct {
	Type         string    `json:"type"` // email, phone, visit
	Date         time.Time `json:"date"`
	RelativeTime string    `json:"relative_time"`
	Summary      string    `json:"summary"`
	Icon         string    `json:"icon"`
	Color        string    `json:"color"`
}

type ServiceInfo struct {
	Date         time.Time `json:"date"`
	RelativeTime string    `json:"relative_time"`
	Type         string    `json:"type"`
	Description  string    `json:"description"`
	Urgency      Priority  `json:"urgency"`
}

type EquipmentSummary struct {
	ID          int64  `json:"id"`
	Type        string `json:"type"`
	Brand       string `json:"brand"`
	Model       string `json:"model"`
	Status      string `json:"status"`
	StatusColor string `json:"status_color"`
	StatusIcon  string `json:"status_icon"`
	LastService string `json:"last_service"`
	NextService string `json:"next_service"`
	HealthScore int    `json:"health_score"`
	HealthColor string `json:"health_color"`
}

type SatisfactionScore struct {
	Score       float64 `json:"score"` // 0-5
	Label       string  `json:"label"` // Poor, Fair, Good, Excellent
	Color       string  `json:"color"`
	Icon        string  `json:"icon"`
	ReviewCount int     `json:"review_count"`
	Trend       string  `json:"trend"` // up, down, stable
}

type LocationInfo struct {
	City        string  `json:"city"`
	District    string  `json:"district"`
	Coordinates *LatLng `json:"coordinates,omitempty"`
	ServiceZone string  `json:"service_zone"`
	TravelTime  string  `json:"travel_time"`
}

type LatLng struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

func main() {
	log.Println("🎨 Starting Enhanced HVAC CRM Dashboard...")
	log.Println("🎯 Focus: Human-Readable Data Presentation")

	router := mux.NewRouter()

	// Enhanced API endpoints
	router.HandleFunc("/api/dashboard", handleDashboard).Methods("GET")
	router.HandleFunc("/api/customers/enhanced", handleEnhancedCustomers).Methods("GET")
	router.HandleFunc("/api/customers/{id}/enhanced", handleEnhancedCustomer).Methods("GET")
	router.HandleFunc("/api/jobs/enhanced", handleEnhancedJobs).Methods("GET")
	router.HandleFunc("/api/analytics/overview", handleAnalyticsOverview).Methods("GET")

	// Static dashboard UI
	router.HandleFunc("/", handleDashboardUI).Methods("GET")
	router.HandleFunc("/dashboard", handleDashboardUI).Methods("GET")
	router.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("./web/static/"))))

	// Health check
	router.HandleFunc("/health", handleHealth).Methods("GET")

	// Setup CORS
	c := cors.New(cors.Options{
		AllowedOrigins: []string{"*"},
		AllowedMethods: []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders: []string{"*"},
	})

	handler := c.Handler(router)

	log.Println("🚀 Enhanced Dashboard Server starting on :8080")
	log.Println("📊 Dashboard UI: http://localhost:8080/dashboard")
	log.Println("🔗 Enhanced API: http://localhost:8080/api/customers/enhanced")
	log.Println("📈 Analytics: http://localhost:8080/api/analytics/overview")
	log.Println("🏥 Health: http://localhost:8080/health")

	if err := http.ListenAndServe(":8080", handler); err != nil {
		log.Fatal("Server failed to start:", err)
	}
}

func handleHealth(w http.ResponseWriter, r *http.Request) {
	response := EnhancedResponse{
		Data: map[string]interface{}{
			"status":  "healthy",
			"service": "enhanced-hvac-dashboard",
			"version": "2.0.0-enhanced",
		},
		Meta: ResponseMeta{
			QueryTime:     "1ms",
			DataFreshness: "real-time",
		},
		Context: map[string]interface{}{
			"server_time": time.Now().Format("15:04:05"),
			"timezone":    "Europe/Warsaw",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleDashboard(w http.ResponseWriter, r *http.Request) {
	// Dashboard overview with key metrics
	dashboardData := map[string]interface{}{
		"summary": map[string]interface{}{
			"active_jobs":           12,
			"pending_jobs":          5,
			"completed_today":       8,
			"revenue_today":         "4,250 PLN",
			"customer_satisfaction": 4.7,
		},
		"alerts": []map[string]interface{}{
			{
				"type":    "urgent",
				"message": "3 pilne naprawy wymagają uwagi",
				"color":   "#dc3545",
				"icon":    "⚠️",
				"action":  "Zobacz szczegóły",
			},
			{
				"type":    "maintenance",
				"message": "15 urządzeń wymaga przeglądu w tym tygodniu",
				"color":   "#ffc107",
				"icon":    "🔧",
				"action":  "Zaplanuj przeglądy",
			},
		},
		"recent_activity": generateRecentActivity(),
	}

	response := EnhancedResponse{
		Data: dashboardData,
		Meta: ResponseMeta{
			QueryTime:     "15ms",
			DataFreshness: "30 seconds ago",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "new_job", Label: "Nowe zlecenie", Icon: "➕", Color: "#28a745", Endpoint: "/api/jobs/create"},
				{ID: "emergency", Label: "Awaria", Icon: "🚨", Color: "#dc3545", Endpoint: "/api/emergency"},
			},
			Visualizations: []VisualizationType{
				{Type: "chart", Config: map[string]interface{}{"type": "line", "field": "revenue"}},
				{Type: "gauge", Config: map[string]interface{}{"field": "satisfaction", "min": 0, "max": 5}},
			},
		},
		Context: map[string]interface{}{
			"user_role":    "manager",
			"current_time": time.Now().Format("15:04"),
			"weather":      "22°C, słonecznie",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func generateRecentActivity() []map[string]interface{} {
	activities := []map[string]interface{}{
		{
			"id":          1,
			"type":        "job_completed",
			"title":       "Naprawa klimatyzacji - Jan Kowalski",
			"time":        "15 minut temu",
			"icon":        "✅",
			"color":       "#28a745",
			"description": "Wymiana filtra i uzupełnienie freonu",
		},
		{
			"id":          2,
			"type":        "customer_feedback",
			"title":       "Nowa opinia - Anna Nowak",
			"time":        "1 godzina temu",
			"icon":        "⭐",
			"color":       "#ffc107",
			"description": "Ocena: 5/5 - Bardzo profesjonalna obsługa",
		},
		{
			"id":          3,
			"type":        "urgent_request",
			"title":       "Pilne zgłoszenie - Biuro XYZ",
			"time":        "2 godziny temu",
			"icon":        "🚨",
			"color":       "#dc3545",
			"description": "Awaria systemu chłodzenia serwerowni",
		},
	}

	return activities
}

// Helper functions for data formatting
func formatRelativeTime(t time.Time) string {
	now := time.Now()
	diff := now.Sub(t)

	if diff < time.Minute {
		return "przed chwilą"
	} else if diff < time.Hour {
		minutes := int(diff.Minutes())
		return fmt.Sprintf("%d minut temu", minutes)
	} else if diff < 24*time.Hour {
		hours := int(diff.Hours())
		return fmt.Sprintf("%d godzin temu", hours)
	} else if diff < 7*24*time.Hour {
		days := int(diff.Hours() / 24)
		return fmt.Sprintf("%d dni temu", days)
	} else {
		return t.Format("02.01.2006")
	}
}

func formatCurrency(amount float64) string {
	return fmt.Sprintf("%.2f PLN", amount)
}

func formatPhone(phone string) string {
	// Format Polish phone numbers
	cleaned := strings.ReplaceAll(phone, " ", "")
	cleaned = strings.ReplaceAll(cleaned, "-", "")

	if len(cleaned) == 9 && !strings.HasPrefix(cleaned, "+") {
		return fmt.Sprintf("+48 %s %s %s", cleaned[0:3], cleaned[3:6], cleaned[6:9])
	}

	return phone
}

func getCustomerStatus(id int64) CustomerStatus {
	// Mock status based on ID
	statuses := []CustomerStatus{
		{Code: "vip", Label: "VIP", Color: "#6f42c1", Icon: "👑", Description: "Klient VIP"},
		{Code: "active", Label: "Aktywny", Color: "#28a745", Icon: "✅", Description: "Aktywny klient"},
		{Code: "new", Label: "Nowy", Color: "#17a2b8", Icon: "🆕", Description: "Nowy klient"},
	}

	return statuses[id%int64(len(statuses))]
}

func getPriority(level int) Priority {
	priorities := map[int]Priority{
		1: {Level: 1, Label: "Niski", Color: "#6c757d", Icon: "⬇️", BadgeClass: "badge-secondary"},
		2: {Level: 2, Label: "Normalny", Color: "#28a745", Icon: "➡️", BadgeClass: "badge-success"},
		3: {Level: 3, Label: "Wysoki", Color: "#ffc107", Icon: "⬆️", BadgeClass: "badge-warning"},
		4: {Level: 4, Label: "Pilny", Color: "#fd7e14", Icon: "🔥", BadgeClass: "badge-warning"},
		5: {Level: 5, Label: "Krytyczny", Color: "#dc3545", Icon: "🚨", BadgeClass: "badge-danger"},
	}

	if p, exists := priorities[level]; exists {
		return p
	}
	return priorities[2] // default to normal
}

func handleEnhancedCustomers(w http.ResponseWriter, r *http.Request) {
	// Enhanced customers with rich metadata
	customers := []EnhancedCustomer{
		{
			ID:             1,
			Name:           "Jan Kowalski",
			Email:          "<EMAIL>",
			Phone:          "+48123456789",
			Address:        "ul. Testowa 1, 00-001 Warszawa",
			CreatedAt:      time.Now().Add(-24 * time.Hour),
			UpdatedAt:      time.Now(),
			DisplayName:    "Jan K.",
			FormattedPhone: formatPhone("+48123456789"),
			RelativeTime:   formatRelativeTime(time.Now().Add(-24 * time.Hour)),
			Status:         getCustomerStatus(1),
			Priority:       getPriority(3),
			Tags:           []string{"VIP", "Długoletni klient", "Klimatyzacja"},
			Stats: CustomerStats{
				TotalJobs:      15,
				CompletedJobs:  14,
				PendingJobs:    1,
				TotalSpent:     12500.00,
				FormattedSpent: formatCurrency(12500.00),
				AvgJobValue:    833.33,
				FormattedAvg:   formatCurrency(833.33),
				LastJobDate:    "3 dni temu",
				CustomerSince:  "2 lata temu",
			},
			LastContact: &ContactInfo{
				Type:         "phone",
				Date:         time.Now().Add(-2 * time.Hour),
				RelativeTime: "2 godziny temu",
				Summary:      "Umówienie przeglądu klimatyzacji",
				Icon:         "📞",
				Color:        "#17a2b8",
			},
			NextService: &ServiceInfo{
				Date:         time.Now().Add(7 * 24 * time.Hour),
				RelativeTime: "za tydzień",
				Type:         "maintenance",
				Description:  "Przegląd okresowy klimatyzacji",
				Urgency:      getPriority(2),
			},
			Equipment: []EquipmentSummary{
				{
					ID:          1,
					Type:        "Klimatyzacja",
					Brand:       "Daikin",
					Model:       "FTXS35K",
					Status:      "Sprawna",
					StatusColor: "#28a745",
					StatusIcon:  "✅",
					LastService: "3 miesiące temu",
					NextService: "za 3 miesiące",
					HealthScore: 85,
					HealthColor: "#28a745",
				},
			},
			Satisfaction: SatisfactionScore{
				Score:       4.8,
				Label:       "Doskonała",
				Color:       "#28a745",
				Icon:        "⭐",
				ReviewCount: 12,
				Trend:       "up",
			},
			Location: LocationInfo{
				City:        "Warszawa",
				District:    "Mokotów",
				ServiceZone: "Strefa A",
				TravelTime:  "15 min",
			},
		},
		{
			ID:             2,
			Name:           "Anna Nowak",
			Email:          "<EMAIL>",
			Phone:          "+48987654321",
			Address:        "ul. Przykładowa 2, 00-002 Kraków",
			CreatedAt:      time.Now().Add(-48 * time.Hour),
			UpdatedAt:      time.Now(),
			DisplayName:    "Anna N.",
			FormattedPhone: formatPhone("+48987654321"),
			RelativeTime:   formatRelativeTime(time.Now().Add(-48 * time.Hour)),
			Status:         getCustomerStatus(2),
			Priority:       getPriority(4),
			Tags:           []string{"Nowy klient", "Pompa ciepła"},
			Stats: CustomerStats{
				TotalJobs:      3,
				CompletedJobs:  2,
				PendingJobs:    1,
				TotalSpent:     3200.00,
				FormattedSpent: formatCurrency(3200.00),
				AvgJobValue:    1066.67,
				FormattedAvg:   formatCurrency(1066.67),
				LastJobDate:    "1 dzień temu",
				CustomerSince:  "2 miesiące temu",
			},
			LastContact: &ContactInfo{
				Type:         "email",
				Date:         time.Now().Add(-4 * time.Hour),
				RelativeTime: "4 godziny temu",
				Summary:      "Zgłoszenie awarii pompy ciepła",
				Icon:         "📧",
				Color:        "#dc3545",
			},
			NextService: &ServiceInfo{
				Date:         time.Now().Add(2 * 24 * time.Hour),
				RelativeTime: "za 2 dni",
				Type:         "repair",
				Description:  "Naprawa pompy ciepła - pilne",
				Urgency:      getPriority(4),
			},
			Equipment: []EquipmentSummary{
				{
					ID:          2,
					Type:        "Pompa ciepła",
					Brand:       "Mitsubishi",
					Model:       "PUHZ-SHW80VHA",
					Status:      "Awaria",
					StatusColor: "#dc3545",
					StatusIcon:  "❌",
					LastService: "1 miesiąc temu",
					NextService: "pilne",
					HealthScore: 25,
					HealthColor: "#dc3545",
				},
			},
			Satisfaction: SatisfactionScore{
				Score:       4.2,
				Label:       "Dobra",
				Color:       "#ffc107",
				Icon:        "⭐",
				ReviewCount: 3,
				Trend:       "stable",
			},
			Location: LocationInfo{
				City:        "Kraków",
				District:    "Nowa Huta",
				ServiceZone: "Strefa B",
				TravelTime:  "45 min",
			},
		},
	}

	response := EnhancedResponse{
		Data: customers,
		Meta: ResponseMeta{
			Total:         len(customers),
			Page:          1,
			PerPage:       10,
			HasNext:       false,
			HasPrev:       false,
			QueryTime:     "8ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Formatting: map[string]interface{}{
				"currency":     "PLN",
				"date_format":  "relative",
				"phone_format": "international",
			},
			Actions: []QuickAction{
				{ID: "call", Label: "Zadzwoń", Icon: "📞", Color: "#17a2b8", Endpoint: "/api/call"},
				{ID: "email", Label: "Email", Icon: "📧", Color: "#6c757d", Endpoint: "/api/email"},
				{ID: "schedule", Label: "Zaplanuj", Icon: "📅", Color: "#28a745", Endpoint: "/api/schedule"},
			},
			Filters: []FilterOption{
				{Field: "status", Label: "Status", Type: "select", Options: []string{"active", "vip", "new", "inactive"}},
				{Field: "priority", Label: "Priorytet", Type: "select", Options: []string{"low", "normal", "high", "urgent", "critical"}},
				{Field: "location", Label: "Lokalizacja", Type: "select", Options: []string{"Warszawa", "Kraków", "Gdańsk"}},
			},
			Sorting: []SortOption{
				{Field: "name", Label: "Nazwa", Direction: "asc", Default: true},
				{Field: "last_contact", Label: "Ostatni kontakt", Direction: "desc"},
				{Field: "satisfaction", Label: "Zadowolenie", Direction: "desc"},
			},
			Visualizations: []VisualizationType{
				{Type: "map", Config: map[string]interface{}{"field": "location", "cluster": true}},
				{Type: "chart", Config: map[string]interface{}{"type": "bar", "field": "satisfaction"}},
			},
		},
		Context: map[string]interface{}{
			"total_revenue":    formatCurrency(15700.00),
			"avg_satisfaction": 4.5,
			"active_customers": 2,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleEnhancedCustomer(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	idStr := vars["id"]
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "Invalid customer ID", http.StatusBadRequest)
		return
	}

	// Mock detailed customer data
	customer := EnhancedCustomer{
		ID:             id,
		Name:           "Jan Kowalski",
		Email:          "<EMAIL>",
		Phone:          "+48123456789",
		Address:        "ul. Testowa 1, 00-001 Warszawa",
		CreatedAt:      time.Now().Add(-24 * time.Hour),
		UpdatedAt:      time.Now(),
		DisplayName:    "Jan K.",
		FormattedPhone: formatPhone("+48123456789"),
		RelativeTime:   formatRelativeTime(time.Now().Add(-24 * time.Hour)),
		Status:         getCustomerStatus(id),
		Priority:       getPriority(3),
		Tags:           []string{"VIP", "Długoletni klient", "Klimatyzacja"},
		// ... rest of the customer data
	}

	response := EnhancedResponse{
		Data: customer,
		Meta: ResponseMeta{
			QueryTime:     "5ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "edit", Label: "Edytuj", Icon: "✏️", Color: "#6c757d", Endpoint: fmt.Sprintf("/api/customers/%d/edit", id)},
				{ID: "new_job", Label: "Nowe zlecenie", Icon: "➕", Color: "#28a745", Endpoint: fmt.Sprintf("/api/customers/%d/jobs/create", id)},
				{ID: "history", Label: "Historia", Icon: "📋", Color: "#17a2b8", Endpoint: fmt.Sprintf("/api/customers/%d/history", id)},
			},
		},
		Context: map[string]interface{}{
			"view_type": "detail",
			"editable":  true,
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleEnhancedJobs(w http.ResponseWriter, r *http.Request) {
	// Enhanced jobs with rich metadata
	jobs := []map[string]interface{}{
		{
			"id":                 1,
			"customer_id":        1,
			"customer_name":      "Jan Kowalski",
			"title":              "Naprawa klimatyzacji",
			"description":        "Klimatyzacja nie chłodzi - wymiana filtra i uzupełnienie freonu",
			"status":             "completed",
			"status_label":       "Zakończone",
			"status_color":       "#28a745",
			"status_icon":        "✅",
			"priority":           getPriority(3),
			"scheduled_at":       time.Now().Add(24 * time.Hour),
			"created_at":         time.Now().Add(-2 * time.Hour),
			"updated_at":         time.Now(),
			"relative_time":      formatRelativeTime(time.Now().Add(-2 * time.Hour)),
			"estimated_duration": "2-3 godziny",
			"actual_duration":    "2.5 godziny",
			"technician": map[string]interface{}{
				"id":     1,
				"name":   "Marek Kowalczyk",
				"phone":  "+48 111 222 333",
				"status": "available",
			},
			"location": map[string]interface{}{
				"address":     "ul. Testowa 1, Warszawa",
				"travel_time": "15 min",
				"coordinates": map[string]float64{"lat": 52.2297, "lng": 21.0122},
			},
			"equipment": []map[string]interface{}{
				{
					"type":   "Klimatyzacja",
					"brand":  "Daikin",
					"model":  "FTXS35K",
					"serial": "DK123456789",
				},
			},
			"parts_used": []map[string]interface{}{
				{"name": "Filtr powietrza", "quantity": 1, "cost": "45.00 PLN"},
				{"name": "Freon R410A", "quantity": "0.5kg", "cost": "120.00 PLN"},
			},
			"total_cost":            "350.00 PLN",
			"customer_satisfaction": 5,
			"notes":                 "Klient bardzo zadowolony. Zalecono wymianę filtra co 6 miesięcy.",
		},
		{
			"id":                 2,
			"customer_id":        2,
			"customer_name":      "Anna Nowak",
			"title":              "Awaria pompy ciepła",
			"description":        "Pompa ciepła nie grzeje - pilna naprawa",
			"status":             "in_progress",
			"status_label":       "W trakcie",
			"status_color":       "#ffc107",
			"status_icon":        "🔧",
			"priority":           getPriority(4),
			"scheduled_at":       time.Now().Add(2 * time.Hour),
			"created_at":         time.Now().Add(-1 * time.Hour),
			"updated_at":         time.Now().Add(-30 * time.Minute),
			"relative_time":      formatRelativeTime(time.Now().Add(-1 * time.Hour)),
			"estimated_duration": "3-4 godziny",
			"actual_duration":    "1.5 godziny (w trakcie)",
			"technician": map[string]interface{}{
				"id":     2,
				"name":   "Piotr Nowak",
				"phone":  "+48 222 333 444",
				"status": "on_site",
			},
			"location": map[string]interface{}{
				"address":     "ul. Przykładowa 2, Kraków",
				"travel_time": "45 min",
				"coordinates": map[string]float64{"lat": 50.0647, "lng": 19.9450},
			},
			"equipment": []map[string]interface{}{
				{
					"type":   "Pompa ciepła",
					"brand":  "Mitsubishi",
					"model":  "PUHZ-SHW80VHA",
					"serial": "MT987654321",
				},
			},
			"parts_used":            []map[string]interface{}{},
			"total_cost":            "0.00 PLN (w trakcie)",
			"customer_satisfaction": 0,
			"notes":                 "Diagnoza w trakcie. Podejrzenie awarii kompresora.",
		},
	}

	response := EnhancedResponse{
		Data: jobs,
		Meta: ResponseMeta{
			Total:         len(jobs),
			Page:          1,
			PerPage:       10,
			HasNext:       false,
			HasPrev:       false,
			QueryTime:     "12ms",
			DataFreshness: "real-time",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "new_job", Label: "Nowe zlecenie", Icon: "➕", Color: "#28a745", Endpoint: "/api/jobs/create"},
				{ID: "emergency", Label: "Awaria", Icon: "🚨", Color: "#dc3545", Endpoint: "/api/jobs/emergency"},
				{ID: "schedule", Label: "Harmonogram", Icon: "📅", Color: "#17a2b8", Endpoint: "/api/schedule"},
			},
			Filters: []FilterOption{
				{Field: "status", Label: "Status", Type: "select", Options: []string{"pending", "in_progress", "completed", "cancelled"}},
				{Field: "priority", Label: "Priorytet", Type: "select", Options: []string{"low", "normal", "high", "urgent", "critical"}},
				{Field: "technician", Label: "Technik", Type: "select", Options: []string{"Marek Kowalczyk", "Piotr Nowak", "Anna Technik"}},
			},
			Visualizations: []VisualizationType{
				{Type: "timeline", Config: map[string]interface{}{"field": "scheduled_at", "groupBy": "technician"}},
				{Type: "map", Config: map[string]interface{}{"field": "location", "routes": true}},
			},
		},
		Context: map[string]interface{}{
			"active_jobs":         1,
			"completed_today":     1,
			"total_revenue_today": "350.00 PLN",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleAnalyticsOverview(w http.ResponseWriter, r *http.Request) {
	// Analytics overview with KPIs and charts
	analytics := map[string]interface{}{
		"kpis": map[string]interface{}{
			"revenue": map[string]interface{}{
				"current":     "15,750 PLN",
				"previous":    "12,300 PLN",
				"change":      "+28%",
				"trend":       "up",
				"color":       "#28a745",
				"icon":        "💰",
				"description": "Przychód w tym miesiącu",
			},
			"jobs_completed": map[string]interface{}{
				"current":     42,
				"previous":    38,
				"change":      "+10.5%",
				"trend":       "up",
				"color":       "#28a745",
				"icon":        "✅",
				"description": "Zakończone zlecenia",
			},
			"customer_satisfaction": map[string]interface{}{
				"current":     4.7,
				"previous":    4.5,
				"change":      "+4.4%",
				"trend":       "up",
				"color":       "#28a745",
				"icon":        "⭐",
				"description": "Średnia ocena klientów",
			},
			"response_time": map[string]interface{}{
				"current":     "1.2h",
				"previous":    "1.8h",
				"change":      "-33%",
				"trend":       "up",
				"color":       "#28a745",
				"icon":        "⚡",
				"description": "Średni czas odpowiedzi",
			},
		},
		"charts": map[string]interface{}{
			"revenue_trend": map[string]interface{}{
				"type":  "line",
				"title": "Trend przychodów",
				"data": []map[string]interface{}{
					{"date": "2024-01", "value": 8500},
					{"date": "2024-02", "value": 9200},
					{"date": "2024-03", "value": 12300},
					{"date": "2024-04", "value": 15750},
				},
				"config": map[string]interface{}{
					"color":  "#28a745",
					"smooth": true,
				},
			},
			"job_status": map[string]interface{}{
				"type":  "pie",
				"title": "Status zleceń",
				"data": []map[string]interface{}{
					{"label": "Zakończone", "value": 42, "color": "#28a745"},
					{"label": "W trakcie", "value": 8, "color": "#ffc107"},
					{"label": "Zaplanowane", "value": 15, "color": "#17a2b8"},
					{"label": "Anulowane", "value": 2, "color": "#6c757d"},
				},
			},
			"technician_performance": map[string]interface{}{
				"type":  "bar",
				"title": "Wydajność techników",
				"data": []map[string]interface{}{
					{"name": "Marek K.", "completed": 18, "satisfaction": 4.8},
					{"name": "Piotr N.", "completed": 15, "satisfaction": 4.6},
					{"name": "Anna T.", "completed": 9, "satisfaction": 4.9},
				},
			},
		},
		"alerts": []map[string]interface{}{
			{
				"type":     "warning",
				"title":    "Niski poziom części",
				"message":  "Filtry powietrza - pozostało 5 sztuk",
				"color":    "#ffc107",
				"icon":     "⚠️",
				"priority": "medium",
			},
			{
				"type":     "info",
				"title":    "Sezonowe przypomnienie",
				"message":  "Czas na kampanię przeglądów przed sezonem",
				"color":    "#17a2b8",
				"icon":     "📅",
				"priority": "low",
			},
		},
	}

	response := EnhancedResponse{
		Data: analytics,
		Meta: ResponseMeta{
			QueryTime:     "25ms",
			DataFreshness: "5 minutes ago",
		},
		UI: UIEnhancements{
			Actions: []QuickAction{
				{ID: "export", Label: "Eksport", Icon: "📊", Color: "#6c757d", Endpoint: "/api/analytics/export"},
				{ID: "refresh", Label: "Odśwież", Icon: "🔄", Color: "#17a2b8", Endpoint: "/api/analytics/refresh"},
			},
			Visualizations: []VisualizationType{
				{Type: "dashboard", Config: map[string]interface{}{"layout": "grid", "responsive": true}},
			},
		},
		Context: map[string]interface{}{
			"period":   "current_month",
			"currency": "PLN",
			"timezone": "Europe/Warsaw",
		},
		Timestamp: time.Now().Format(time.RFC3339),
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

func handleDashboardUI(w http.ResponseWriter, r *http.Request) {
	// Serve the enhanced dashboard HTML
	html := `<!DOCTYPE html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎨 Enhanced HVAC CRM Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #17a2b8;
            --light-bg: #f8f9fa;
            --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }

        body {
            background-color: var(--light-bg);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: var(--card-shadow);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .kpi-card {
            background: linear-gradient(135deg, #fff, #f8f9fa);
            border-left: 4px solid var(--primary-color);
        }

        .status-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .priority-high { background-color: #fff3cd; color: #856404; }
        .priority-urgent { background-color: #f8d7da; color: #721c24; }
        .priority-normal { background-color: #d1ecf1; color: #0c5460; }

        .customer-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--secondary-color), var(--success-color));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .activity-item {
            border-left: 3px solid var(--info-color);
            padding-left: 1rem;
            margin-bottom: 1rem;
        }

        .metric-icon {
            font-size: 2rem;
            opacity: 0.8;
        }

        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .refresh-btn {
            transition: transform 0.2s ease;
        }

        .refresh-btn:hover {
            transform: rotate(180deg);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-snowflake me-2"></i>
                <strong>HVAC CRM Enhanced</strong>
            </a>
            <div class="d-flex align-items-center">
                <span class="text-light me-3">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </span>
                <button class="btn btn-outline-light btn-sm refresh-btn" onclick="refreshDashboard()">
                    <i class="fas fa-sync-alt"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- KPI Cards -->
        <div class="row mb-4" id="kpi-section">
            <div class="col-md-3 mb-3">
                <div class="card kpi-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">Aktywne zlecenia</h6>
                                <h3 class="card-title mb-0" id="active-jobs">12</h3>
                                <small class="text-success">+3 od wczoraj</small>
                            </div>
                            <div class="metric-icon text-primary">
                                <i class="fas fa-tasks"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card kpi-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">Przychód dziś</h6>
                                <h3 class="card-title mb-0" id="revenue-today">4,250 PLN</h3>
                                <small class="text-success">+15% vs wczoraj</small>
                            </div>
                            <div class="metric-icon text-success">
                                <i class="fas fa-chart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card kpi-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">Zadowolenie</h6>
                                <h3 class="card-title mb-0" id="satisfaction">4.7 ⭐</h3>
                                <small class="text-success">+0.2 w tym miesiącu</small>
                            </div>
                            <div class="metric-icon text-warning">
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-3 mb-3">
                <div class="card kpi-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-subtitle mb-2 text-muted">Czas odpowiedzi</h6>
                                <h3 class="card-title mb-0" id="response-time">1.2h</h3>
                                <small class="text-success">-33% vs miesiąc</small>
                            </div>
                            <div class="metric-icon text-info">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Dashboard Content -->
        <div class="row">
            <!-- Recent Activity -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Ostatnia aktywność
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="loadActivity()">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    <div class="card-body" id="activity-list">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Ładowanie...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="col-lg-6 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>
                            Szybkie akcje
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-2">
                            <div class="col-6">
                                <button class="btn btn-success w-100" onclick="newJob()">
                                    <i class="fas fa-plus me-2"></i>
                                    Nowe zlecenie
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-danger w-100" onclick="emergency()">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Awaria
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-info w-100" onclick="viewCustomers()">
                                    <i class="fas fa-users me-2"></i>
                                    Klienci
                                </button>
                            </div>
                            <div class="col-6">
                                <button class="btn btn-warning w-100" onclick="viewSchedule()">
                                    <i class="fas fa-calendar me-2"></i>
                                    Harmonogram
                                </button>
                            </div>
                        </div>

                        <hr>

                        <div class="row g-2">
                            <div class="col-12">
                                <button class="btn btn-outline-primary w-100" onclick="viewAnalytics()">
                                    <i class="fas fa-chart-bar me-2"></i>
                                    Analityka i raporty
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Data Tables -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-table me-2"></i>
                            Klienci z rozszerzonymi danymi
                        </h5>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary me-2" onclick="exportData()">
                                <i class="fas fa-download me-1"></i>
                                Eksport
                            </button>
                            <button class="btn btn-sm btn-primary" onclick="loadCustomers()">
                                <i class="fas fa-refresh me-1"></i>
                                Odśwież
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="customers-table">
                                <thead class="table-light">
                                    <tr>
                                        <th>Klient</th>
                                        <th>Status</th>
                                        <th>Priorytet</th>
                                        <th>Ostatni kontakt</th>
                                        <th>Zadowolenie</th>
                                        <th>Następny serwis</th>
                                        <th>Akcje</th>
                                    </tr>
                                </thead>
                                <tbody id="customers-tbody">
                                    <tr>
                                        <td colspan="7" class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">Ładowanie...</span>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleTimeString('pl-PL');
        }

        setInterval(updateTime, 1000);
        updateTime();

        // Load dashboard data
        async function loadDashboard() {
            try {
                const response = await fetch('/api/dashboard');
                const data = await response.json();

                // Update KPIs
                if (data.data.summary) {
                    const summary = data.data.summary;
                    document.getElementById('active-jobs').textContent = summary.active_jobs;
                    document.getElementById('revenue-today').textContent = summary.revenue_today;
                    document.getElementById('satisfaction').textContent = summary.customer_satisfaction + ' ⭐';
                }

                // Load recent activity
                loadActivity();

            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        // Load recent activity
        async function loadActivity() {
            try {
                const response = await fetch('/api/dashboard');
                const data = await response.json();

                const activityList = document.getElementById('activity-list');
                if (data.data.recent_activity) {
                    activityList.innerHTML = data.data.recent_activity.map(activity =>
                        '<div class="activity-item">' +
                        '<div class="d-flex justify-content-between align-items-start">' +
                        '<div>' +
                        '<h6 class="mb-1">' + activity.icon + ' ' + activity.title + '</h6>' +
                        '<p class="mb-1 text-muted small">' + activity.description + '</p>' +
                        '<small class="text-muted">' + activity.time + '</small>' +
                        '</div>' +
                        '</div>' +
                        '</div>'
                    ).join('');
                }
            } catch (error) {
                console.error('Error loading activity:', error);
            }
        }

        // Load customers with enhanced data
        async function loadCustomers() {
            try {
                const tbody = document.getElementById('customers-tbody');
                tbody.innerHTML = '<tr><td colspan="7" class="text-center"><div class="spinner-border text-primary" role="status"></div></td></tr>';

                const response = await fetch('/api/customers/enhanced');
                const data = await response.json();

                if (data.data) {
                    tbody.innerHTML = data.data.map(customer =>
                        '<tr class="fade-in">' +
                        '<td>' +
                        '<div class="d-flex align-items-center">' +
                        '<div class="customer-avatar me-3">' + customer.display_name.charAt(0) + '</div>' +
                        '<div>' +
                        '<div class="fw-bold">' + customer.name + '</div>' +
                        '<small class="text-muted">' + customer.formatted_phone + '</small>' +
                        '</div>' +
                        '</div>' +
                        '</td>' +
                        '<td><span class="status-badge" style="background-color: ' + customer.status.color + '20; color: ' + customer.status.color + '">' +
                        customer.status.icon + ' ' + customer.status.label + '</span></td>' +
                        '<td><span class="status-badge priority-' + customer.priority.label.toLowerCase() + '">' +
                        customer.priority.icon + ' ' + customer.priority.label + '</span></td>' +
                        '<td>' +
                        '<div>' + (customer.last_contact ? customer.last_contact.summary : 'Brak') + '</div>' +
                        '<small class="text-muted">' + (customer.last_contact ? customer.last_contact.relative_time : '') + '</small>' +
                        '</td>' +
                        '<td>' +
                        '<div class="d-flex align-items-center">' +
                        '<span class="me-2">' + customer.satisfaction.score + '</span>' +
                        '<span style="color: ' + customer.satisfaction.color + '">' + customer.satisfaction.icon + '</span>' +
                        '</div>' +
                        '</td>' +
                        '<td>' +
                        '<div>' + (customer.next_service ? customer.next_service.description : 'Brak zaplanowanych') + '</div>' +
                        '<small class="text-muted">' + (customer.next_service ? customer.next_service.relative_time : '') + '</small>' +
                        '</td>' +
                        '<td>' +
                        '<div class="btn-group btn-group-sm">' +
                        '<button class="btn btn-outline-primary" onclick="viewCustomer(' + customer.id + ')"><i class="fas fa-eye"></i></button>' +
                        '<button class="btn btn-outline-success" onclick="callCustomer(' + customer.id + ')"><i class="fas fa-phone"></i></button>' +
                        '<button class="btn btn-outline-info" onclick="emailCustomer(' + customer.id + ')"><i class="fas fa-envelope"></i></button>' +
                        '</div>' +
                        '</td>' +
                        '</tr>'
                    ).join('');
                }
            } catch (error) {
                console.error('Error loading customers:', error);
            }
        }

        // Action functions
        function refreshDashboard() {
            loadDashboard();
            loadCustomers();
        }

        function newJob() {
            alert('🆕 Nowe zlecenie - funkcja w przygotowaniu');
        }

        function emergency() {
            alert('🚨 Zgłoszenie awarii - funkcja w przygotowaniu');
        }

        function viewCustomers() {
            alert('👥 Widok klientów - funkcja w przygotowaniu');
        }

        function viewSchedule() {
            alert('📅 Harmonogram - funkcja w przygotowaniu');
        }

        function viewAnalytics() {
            window.open('/api/analytics/overview', '_blank');
        }

        function exportData() {
            alert('📊 Eksport danych - funkcja w przygotowaniu');
        }

        function viewCustomer(id) {
            window.open('/api/customers/' + id + '/enhanced', '_blank');
        }

        function callCustomer(id) {
            alert('📞 Dzwonienie do klienta ID: ' + id);
        }

        function emailCustomer(id) {
            alert('📧 Email do klienta ID: ' + id);
        }

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            loadCustomers();

            // Auto-refresh every 30 seconds
            setInterval(refreshDashboard, 30000);
        });
    </script>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(html))
}
