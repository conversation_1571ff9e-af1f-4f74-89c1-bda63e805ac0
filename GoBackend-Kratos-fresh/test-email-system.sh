#!/bin/bash

# 🧪 Test Email Intelligence System
# Komprehensywny test systemu pobierania i analizy maili

echo "🚀 ========================================"
echo "📧 HVAC Email Intelligence System Test"
echo "🔧 GoBackend-Kratos Integration Test"
echo "========================================"

# Kolory dla lepszej czytelności
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Funkcje pomocnicze
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Sprawdź czy jesteśmy w odpowiednim katalogu
if [ ! -f "go.mod" ]; then
    print_error "Uruchom script z katalogu GoBackend-Kratos-fresh"
    exit 1
fi

print_info "Sprawdzanie środowiska..."

# Sprawdź czy Go jest zainstalowane
if ! command -v go &> /dev/null; then
    print_error "Go nie jest zainstalowane"
    exit 1
fi
print_success "Go jest zainstalowane: $(go version)"

# Sprawdź czy PostgreSQL jest dostępny
print_info "Testowanie połączenia z bazą danych..."
if command -v psql &> /dev/null; then
    if PGPASSWORD=blaeritipol psql -h ************** -U hvacdb -d hvacdb -c "SELECT 1;" &> /dev/null; then
        print_success "Połączenie z bazą danych działa"
    else
        print_warning "Nie można połączyć się z bazą danych (może być niedostępna)"
    fi
else
    print_warning "psql nie jest zainstalowane - pomijam test bazy danych"
fi

# Test 1: Kompilacja projektu
echo ""
print_info "Test 1: Kompilacja projektu..."
if go build -o bin/test-email-intelligence cmd/email-intelligence/main.go; then
    print_success "Projekt kompiluje się poprawnie"
else
    print_error "Błąd kompilacji"
    exit 1
fi

# Test 2: Test jednostkowy email service
echo ""
print_info "Test 2: Test email service..."
if go run cmd/email-test/main.go 2>/dev/null; then
    print_success "Email service działa poprawnie"
else
    print_warning "Email service test nie przeszedł (może brakować bazy danych)"
fi

# Test 3: Sprawdź konfigurację
echo ""
print_info "Test 3: Sprawdzanie konfiguracji..."
if [ -f "configs/test-email-intelligence.yaml" ]; then
    print_success "Konfiguracja testowa istnieje"
else
    print_error "Brak konfiguracji testowej"
    exit 1
fi

# Test 4: Uruchom serwis w trybie testowym (krótko)
echo ""
print_info "Test 4: Test uruchomienia serwisu..."
timeout 5s ./bin/test-email-intelligence -conf configs/test-email-intelligence.yaml &> /tmp/email-service-test.log &
SERVICE_PID=$!
sleep 3

if kill -0 $SERVICE_PID 2>/dev/null; then
    print_success "Serwis uruchamia się poprawnie"
    kill $SERVICE_PID 2>/dev/null
else
    print_warning "Serwis nie uruchomił się (sprawdź logi)"
    if [ -f "/tmp/email-service-test.log" ]; then
        echo "Ostatnie logi:"
        tail -5 /tmp/email-service-test.log
    fi
fi

# Test 5: Sprawdź czy AI service jest dostępny
echo ""
print_info "Test 5: Sprawdzanie dostępności AI service..."
if curl -s --connect-timeout 3 http://192.168.0.179:1234/api/tags &> /dev/null; then
    print_success "AI service (Ollama) jest dostępny"
else
    print_warning "AI service nie jest dostępny na http://192.168.0.179:1234"
fi

# Test 6: Sprawdź strukturę katalogów
echo ""
print_info "Test 6: Sprawdzanie struktury katalogów..."
mkdir -p data/test data/test_vectordb logs
print_success "Katalogi danych utworzone"

# Test 7: Sprawdź zmienne środowiskowe
echo ""
print_info "Test 7: Sprawdzanie zmiennych środowiskowych..."
if [ -n "$TEST_EMAIL_USERNAME" ] && [ -n "$TEST_EMAIL_PASSWORD" ]; then
    print_success "Zmienne środowiskowe email są ustawione"
    CREDENTIALS_SET=true
else
    print_warning "Zmienne środowiskowe email nie są ustawione"
    print_info "Ustaw je przez:"
    echo "export TEST_EMAIL_USERNAME=<EMAIL>"
    echo "export TEST_EMAIL_PASSWORD=haslo-aplikacji-gmail"
    CREDENTIALS_SET=false
fi

# Podsumowanie
echo ""
echo "🎯 ========================================"
echo "📊 PODSUMOWANIE TESTÓW"
echo "========================================"

if [ "$CREDENTIALS_SET" = true ]; then
    print_success "System jest gotowy do pełnych testów z prawdziwymi emailami"
    echo ""
    print_info "Aby uruchomić pełny test:"
    echo "1. ./bin/test-email-intelligence -conf configs/test-email-intelligence.yaml"
    echo "2. curl http://localhost:8083/health"
    echo "3. curl -X POST http://localhost:8083/api/v1/retrieval/start"
else
    print_warning "System jest gotowy do testów, ale potrzebuje danych logowania email"
    echo ""
    print_info "Aby skonfigurować email:"
    echo "1. Utwórz hasło aplikacji w Gmail"
    echo "2. export TEST_EMAIL_USERNAME=<EMAIL>"
    echo "3. export TEST_EMAIL_PASSWORD=haslo-aplikacji"
    echo "4. Uruchom ponownie test"
fi

echo ""
print_info "Dostępne komendy testowe:"
echo "# Test bazy danych:"
echo "go run cmd/email-test/main.go"
echo ""
echo "# Uruchom serwis:"
echo "./bin/test-email-intelligence -conf configs/test-email-intelligence.yaml"
echo ""
echo "# Test API:"
echo "curl http://localhost:8083/health"
echo "curl http://localhost:8083/api/v1/email-analysis/dashboard/stats"

echo ""
print_success "Test środowiska zakończony!"
