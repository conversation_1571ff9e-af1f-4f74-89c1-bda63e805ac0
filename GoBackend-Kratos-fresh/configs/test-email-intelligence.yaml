# 🧪 Test Email Intelligence Configuration
# Konfiguracja testowa dla systemu pobierania i analizy maili

# 🔌 Database Configuration
database:
  dsn: "host=************** user=hvacdb password=blaeritipol dbname=hvacdb port=5432 sslmode=disable TimeZone=UTC"

# 📧 Test Email Configuration (Gmail)
test_email:
  # Użyj prawdziwego konta Gmail do testów
  host: "imap.gmail.com"
  port: 993
  username: ""  # Ustaw przez zmienną środowiskową TEST_EMAIL_USERNAME
  password: ""  # Ustaw przez zmienną środowiskową TEST_EMAIL_PASSWORD
  use_ssl: true
  folder: "INBOX"
  poll_interval: "2m"  # Częste sprawdzanie dla testów
  enabled: false  # Włącz po ustawieniu danych logowania

# 🤖 AI Analysis Configuration
ai_analysis:
  # Gemma 3 Configuration
  gemma3:
    ollama_url: "http://*************:1234"
    model_name: "gemma3:4b-instruct"
    max_tokens: 4096
    temperature: 0.7
    top_p: 0.9
    timeout: "30s"
    retry_attempts: 2

  # Email Analysis Settings
  email_analysis:
    max_attachment_size: 5242880  # 5MB dla testów
    supported_formats: ["txt", "pdf", "docx"]
    vector_db_path: "./data/test_vectordb"

# 🌐 HTTP Server Configuration
http:
  port: 8083  # Inny port niż produkcyjny
  enable_cors: true
  log_level: "debug"  # Więcej logów dla testów

# ⚡ Processing Configuration
processing:
  max_concurrent_analysis: 2  # Mniej dla testów
  processing_timeout: "20s"
  retry_attempts: 2
  batch_size: 5

# 📊 Test Dashboard Configuration
dashboard:
  refresh_interval: "10s"  # Szybsze odświeżanie dla testów
  max_recent_emails: 20
  enable_real_time: true

# 🔐 Security Configuration (Test Mode)
security:
  enable_auth: false  # Wyłączone dla testów
  session_timeout: "1h"

# 📁 Storage Configuration
storage:
  data_directory: "./data/test"
  backup_enabled: false  # Wyłączone dla testów
  temp_files_retention: "1h"

# 🏷️ HVAC-Specific Test Configuration
hvac_test:
  # Test Keywords dla rozpoznawania HVAC
  test_keywords:
    hvac_equipment:
      - "klimatyzacja"
      - "ogrzewanie"
      - "wentylacja"
      - "pompa ciepła"
      - "kocioł"
      - "termostat"
      - "filtr"
    
    urgency_indicators:
      - "awaria"
      - "pilne"
      - "natychmiast"
      - "nie działa"
      - "zepsute"
      - "zimno"
      - "gorąco"
    
    service_types:
      - "naprawa"
      - "serwis"
      - "konserwacja"
      - "montaż"
      - "przegląd"

# 🧪 Test Scenarios
test_scenarios:
  # Scenariusz 1: Email z awarią
  emergency_email:
    subject: "PILNE - Awaria klimatyzacji"
    content: "Dzień dobry, mamy awarię klimatyzacji w biurze. Nie działa od wczoraj i jest bardzo gorąco. Proszę o pilną naprawę."
    expected_priority: "high"
    expected_category: "emergency"
    expected_hvac_relevance: true

  # Scenariusz 2: Email z zapytaniem o serwis
  service_inquiry:
    subject: "Zapytanie o serwis klimatyzacji"
    content: "Witam, chciałbym umówić przegląd klimatyzacji przed sezonem letnim. Proszę o kontakt."
    expected_priority: "medium"
    expected_category: "maintenance"
    expected_hvac_relevance: true

  # Scenariusz 3: Email niezwiązany z HVAC
  non_hvac_email:
    subject: "Faktura za prąd"
    content: "W załączniku przesyłam fakturę za energię elektryczną za ostatni miesiąc."
    expected_priority: "low"
    expected_category: "general"
    expected_hvac_relevance: false

# 📈 Test Monitoring
test_monitoring:
  # Metryki testowe
  metrics:
    enabled: true
    collection_interval: "5s"
    
  # Health checks
  health_checks:
    database: "5s"
    ai_service: "10s"
    
  # Test alerts
  alerts:
    processing_errors:
      threshold: 2
      window: "1m"

# 🔧 Development Configuration
development:
  debug_mode: true
  mock_ai_responses: false  # Używaj prawdziwego AI
  test_data_path: "./test/data"
  log_ai_requests: true
  log_email_content: false  # Ze względów bezpieczeństwa

# 🎯 Test Instructions
# 
# 1. Ustaw zmienne środowiskowe:
#    export TEST_EMAIL_USERNAME=<EMAIL>
#    export TEST_EMAIL_PASSWORD=haslo-aplikacji-gmail
#
# 2. Uruchom test:
#    cd GoBackend-Kratos-fresh
#    go run cmd/email-test/main.go
#
# 3. Uruchom serwis email intelligence:
#    ./bin/email-intelligence -conf configs/test-email-intelligence.yaml
#
# 4. Testuj API:
#    curl http://localhost:8083/health
#    curl http://localhost:8083/api/v1/email-analysis/dashboard/stats
#
# 5. Testuj pobieranie maili (po ustawieniu danych logowania):
#    curl -X POST http://localhost:8083/api/v1/retrieval/start
