// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.24.4
// source: google/api/field_info.proto

package annotations

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The standard format of a field value. The supported formats are all backed
// by either an RFC defined by the IETF or a Google-defined AIP.
type FieldInfo_Format int32

const (
	// Default, unspecified value.
	FieldInfo_FORMAT_UNSPECIFIED FieldInfo_Format = 0
	// Universally Unique Identifier, version 4, value as defined by
	// https://datatracker.ietf.org/doc/html/rfc4122. The value may be
	// normalized to entirely lowercase letters. For example, the value
	// `F47AC10B-58CC-0372-8567-0E02B2C3D479` would be normalized to
	// `f47ac10b-58cc-0372-8567-0e02b2c3d479`.
	FieldInfo_UUID4 FieldInfo_Format = 1
	// Internet Protocol v4 value as defined by [RFC
	// 791](https://datatracker.ietf.org/doc/html/rfc791). The value may be
	// condensed, with leading zeros in each octet stripped. For example,
	// `***************` would be condensed to `***********`.
	FieldInfo_IPV4 FieldInfo_Format = 2
	// Internet Protocol v6 value as defined by [RFC
	// 2460](https://datatracker.ietf.org/doc/html/rfc2460). The value may be
	// normalized to entirely lowercase letters with zeros compressed, following
	// [RFC 5952](https://datatracker.ietf.org/doc/html/rfc5952). For example,
	// the value `2001:0DB8:0::0` would be normalized to `2001:db8::`.
	FieldInfo_IPV6 FieldInfo_Format = 3
	// An IP address in either v4 or v6 format as described by the individual
	// values defined herein. See the comments on the IPV4 and IPV6 types for
	// allowed normalizations of each.
	FieldInfo_IPV4_OR_IPV6 FieldInfo_Format = 4
)

// Enum value maps for FieldInfo_Format.
var (
	FieldInfo_Format_name = map[int32]string{
		0: "FORMAT_UNSPECIFIED",
		1: "UUID4",
		2: "IPV4",
		3: "IPV6",
		4: "IPV4_OR_IPV6",
	}
	FieldInfo_Format_value = map[string]int32{
		"FORMAT_UNSPECIFIED": 0,
		"UUID4":              1,
		"IPV4":               2,
		"IPV6":               3,
		"IPV4_OR_IPV6":       4,
	}
)

func (x FieldInfo_Format) Enum() *FieldInfo_Format {
	p := new(FieldInfo_Format)
	*p = x
	return p
}

func (x FieldInfo_Format) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FieldInfo_Format) Descriptor() protoreflect.EnumDescriptor {
	return file_google_api_field_info_proto_enumTypes[0].Descriptor()
}

func (FieldInfo_Format) Type() protoreflect.EnumType {
	return &file_google_api_field_info_proto_enumTypes[0]
}

func (x FieldInfo_Format) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FieldInfo_Format.Descriptor instead.
func (FieldInfo_Format) EnumDescriptor() ([]byte, []int) {
	return file_google_api_field_info_proto_rawDescGZIP(), []int{0, 0}
}

// Rich semantic information of an API field beyond basic typing.
type FieldInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The standard format of a field value. This does not explicitly configure
	// any API consumer, just documents the API's format for the field it is
	// applied to.
	Format FieldInfo_Format `protobuf:"varint,1,opt,name=format,proto3,enum=google.api.FieldInfo_Format" json:"format,omitempty"`
	// The type(s) that the annotated, generic field may represent.
	//
	// Currently, this must only be used on fields of type `google.protobuf.Any`.
	// Supporting other generic types may be considered in the future.
	ReferencedTypes []*TypeReference `protobuf:"bytes,2,rep,name=referenced_types,json=referencedTypes,proto3" json:"referenced_types,omitempty"`
}

func (x *FieldInfo) Reset() {
	*x = FieldInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_field_info_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *FieldInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FieldInfo) ProtoMessage() {}

func (x *FieldInfo) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_field_info_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FieldInfo.ProtoReflect.Descriptor instead.
func (*FieldInfo) Descriptor() ([]byte, []int) {
	return file_google_api_field_info_proto_rawDescGZIP(), []int{0}
}

func (x *FieldInfo) GetFormat() FieldInfo_Format {
	if x != nil {
		return x.Format
	}
	return FieldInfo_FORMAT_UNSPECIFIED
}

func (x *FieldInfo) GetReferencedTypes() []*TypeReference {
	if x != nil {
		return x.ReferencedTypes
	}
	return nil
}

// A reference to a message type, for use in [FieldInfo][google.api.FieldInfo].
type TypeReference struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The name of the type that the annotated, generic field may represent.
	// If the type is in the same protobuf package, the value can be the simple
	// message name e.g., `"MyMessage"`. Otherwise, the value must be the
	// fully-qualified message name e.g., `"google.library.v1.Book"`.
	//
	// If the type(s) are unknown to the service (e.g. the field accepts generic
	// user input), use the wildcard `"*"` to denote this behavior.
	//
	// See [AIP-202](https://google.aip.dev/202#type-references) for more details.
	TypeName string `protobuf:"bytes,1,opt,name=type_name,json=typeName,proto3" json:"type_name,omitempty"`
}

func (x *TypeReference) Reset() {
	*x = TypeReference{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_field_info_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TypeReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TypeReference) ProtoMessage() {}

func (x *TypeReference) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_field_info_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TypeReference.ProtoReflect.Descriptor instead.
func (*TypeReference) Descriptor() ([]byte, []int) {
	return file_google_api_field_info_proto_rawDescGZIP(), []int{1}
}

func (x *TypeReference) GetTypeName() string {
	if x != nil {
		return x.TypeName
	}
	return ""
}

var file_google_api_field_info_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.FieldOptions)(nil),
		ExtensionType: (*FieldInfo)(nil),
		Field:         291403980,
		Name:          "google.api.field_info",
		Tag:           "bytes,291403980,opt,name=field_info",
		Filename:      "google/api/field_info.proto",
	},
}

// Extension fields to descriptorpb.FieldOptions.
var (
	// Rich semantic descriptor of an API field beyond the basic typing.
	//
	// Examples:
	//
	//	string request_id = 1 [(google.api.field_info).format = UUID4];
	//	string old_ip_address = 2 [(google.api.field_info).format = IPV4];
	//	string new_ip_address = 3 [(google.api.field_info).format = IPV6];
	//	string actual_ip_address = 4 [
	//	  (google.api.field_info).format = IPV4_OR_IPV6
	//	];
	//	google.protobuf.Any generic_field = 5 [
	//	  (google.api.field_info).referenced_types = {type_name: "ActualType"},
	//	  (google.api.field_info).referenced_types = {type_name: "OtherType"},
	//	];
	//	google.protobuf.Any generic_user_input = 5 [
	//	  (google.api.field_info).referenced_types = {type_name: "*"},
	//	];
	//
	// optional google.api.FieldInfo field_info = 291403980;
	E_FieldInfo = &file_google_api_field_info_proto_extTypes[0]
)

var File_google_api_field_info_proto protoreflect.FileDescriptor

var file_google_api_field_info_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xda, 0x01, 0x0a, 0x09,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x06, 0x66, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f,
	0x2e, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x44, 0x0a, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72,
	0x65, 0x6e, 0x63, 0x65, 0x52, 0x0f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x64,
	0x54, 0x79, 0x70, 0x65, 0x73, 0x22, 0x51, 0x0a, 0x06, 0x46, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12,
	0x16, 0x0a, 0x12, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43,
	0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x55, 0x55, 0x49, 0x44, 0x34,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x49, 0x50, 0x56, 0x34, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04,
	0x49, 0x50, 0x56, 0x36, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x49, 0x50, 0x56, 0x34, 0x5f, 0x4f,
	0x52, 0x5f, 0x49, 0x50, 0x56, 0x36, 0x10, 0x04, 0x22, 0x2c, 0x0a, 0x0d, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x79,
	0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x3a, 0x57, 0x0a, 0x0a, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0xcc, 0xf1, 0xf9, 0x8a, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x42,
	0x6c, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x42, 0x0e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x50, 0x01, 0x5a, 0x41, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61,
	0x6e, 0x67, 0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0xa2, 0x02, 0x04, 0x47, 0x41, 0x50, 0x49, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_google_api_field_info_proto_rawDescOnce sync.Once
	file_google_api_field_info_proto_rawDescData = file_google_api_field_info_proto_rawDesc
)

func file_google_api_field_info_proto_rawDescGZIP() []byte {
	file_google_api_field_info_proto_rawDescOnce.Do(func() {
		file_google_api_field_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_api_field_info_proto_rawDescData)
	})
	return file_google_api_field_info_proto_rawDescData
}

var file_google_api_field_info_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_google_api_field_info_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_google_api_field_info_proto_goTypes = []interface{}{
	(FieldInfo_Format)(0),             // 0: google.api.FieldInfo.Format
	(*FieldInfo)(nil),                 // 1: google.api.FieldInfo
	(*TypeReference)(nil),             // 2: google.api.TypeReference
	(*descriptorpb.FieldOptions)(nil), // 3: google.protobuf.FieldOptions
}
var file_google_api_field_info_proto_depIdxs = []int32{
	0, // 0: google.api.FieldInfo.format:type_name -> google.api.FieldInfo.Format
	2, // 1: google.api.FieldInfo.referenced_types:type_name -> google.api.TypeReference
	3, // 2: google.api.field_info:extendee -> google.protobuf.FieldOptions
	1, // 3: google.api.field_info:type_name -> google.api.FieldInfo
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	3, // [3:4] is the sub-list for extension type_name
	2, // [2:3] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_google_api_field_info_proto_init() }
func file_google_api_field_info_proto_init() {
	if File_google_api_field_info_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_api_field_info_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*FieldInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_field_info_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TypeReference); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_api_field_info_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_google_api_field_info_proto_goTypes,
		DependencyIndexes: file_google_api_field_info_proto_depIdxs,
		EnumInfos:         file_google_api_field_info_proto_enumTypes,
		MessageInfos:      file_google_api_field_info_proto_msgTypes,
		ExtensionInfos:    file_google_api_field_info_proto_extTypes,
	}.Build()
	File_google_api_field_info_proto = out.File
	file_google_api_field_info_proto_rawDesc = nil
	file_google_api_field_info_proto_goTypes = nil
	file_google_api_field_info_proto_depIdxs = nil
}
