// Copyright 2024 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.26.0
// 	protoc        v4.24.4
// source: google/api/client.proto

package annotations

import (
	reflect "reflect"
	sync "sync"

	api "google.golang.org/genproto/googleapis/api"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The organization for which the client libraries are being published.
// Affects the url where generated docs are published, etc.
type ClientLibraryOrganization int32

const (
	// Not useful.
	ClientLibraryOrganization_CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED ClientLibraryOrganization = 0
	// Google Cloud Platform Org.
	ClientLibraryOrganization_CLOUD ClientLibraryOrganization = 1
	// Ads (Advertising) Org.
	ClientLibraryOrganization_ADS ClientLibraryOrganization = 2
	// Photos Org.
	ClientLibraryOrganization_PHOTOS ClientLibraryOrganization = 3
	// Street View Org.
	ClientLibraryOrganization_STREET_VIEW ClientLibraryOrganization = 4
	// Shopping Org.
	ClientLibraryOrganization_SHOPPING ClientLibraryOrganization = 5
	// Geo Org.
	ClientLibraryOrganization_GEO ClientLibraryOrganization = 6
	// Generative AI - https://developers.generativeai.google
	ClientLibraryOrganization_GENERATIVE_AI ClientLibraryOrganization = 7
)

// Enum value maps for ClientLibraryOrganization.
var (
	ClientLibraryOrganization_name = map[int32]string{
		0: "CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED",
		1: "CLOUD",
		2: "ADS",
		3: "PHOTOS",
		4: "STREET_VIEW",
		5: "SHOPPING",
		6: "GEO",
		7: "GENERATIVE_AI",
	}
	ClientLibraryOrganization_value = map[string]int32{
		"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED": 0,
		"CLOUD":         1,
		"ADS":           2,
		"PHOTOS":        3,
		"STREET_VIEW":   4,
		"SHOPPING":      5,
		"GEO":           6,
		"GENERATIVE_AI": 7,
	}
)

func (x ClientLibraryOrganization) Enum() *ClientLibraryOrganization {
	p := new(ClientLibraryOrganization)
	*p = x
	return p
}

func (x ClientLibraryOrganization) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientLibraryOrganization) Descriptor() protoreflect.EnumDescriptor {
	return file_google_api_client_proto_enumTypes[0].Descriptor()
}

func (ClientLibraryOrganization) Type() protoreflect.EnumType {
	return &file_google_api_client_proto_enumTypes[0]
}

func (x ClientLibraryOrganization) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientLibraryOrganization.Descriptor instead.
func (ClientLibraryOrganization) EnumDescriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{0}
}

// To where should client libraries be published?
type ClientLibraryDestination int32

const (
	// Client libraries will neither be generated nor published to package
	// managers.
	ClientLibraryDestination_CLIENT_LIBRARY_DESTINATION_UNSPECIFIED ClientLibraryDestination = 0
	// Generate the client library in a repo under github.com/googleapis,
	// but don't publish it to package managers.
	ClientLibraryDestination_GITHUB ClientLibraryDestination = 10
	// Publish the library to package managers like nuget.org and npmjs.com.
	ClientLibraryDestination_PACKAGE_MANAGER ClientLibraryDestination = 20
)

// Enum value maps for ClientLibraryDestination.
var (
	ClientLibraryDestination_name = map[int32]string{
		0:  "CLIENT_LIBRARY_DESTINATION_UNSPECIFIED",
		10: "GITHUB",
		20: "PACKAGE_MANAGER",
	}
	ClientLibraryDestination_value = map[string]int32{
		"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED": 0,
		"GITHUB":                                 10,
		"PACKAGE_MANAGER":                        20,
	}
)

func (x ClientLibraryDestination) Enum() *ClientLibraryDestination {
	p := new(ClientLibraryDestination)
	*p = x
	return p
}

func (x ClientLibraryDestination) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClientLibraryDestination) Descriptor() protoreflect.EnumDescriptor {
	return file_google_api_client_proto_enumTypes[1].Descriptor()
}

func (ClientLibraryDestination) Type() protoreflect.EnumType {
	return &file_google_api_client_proto_enumTypes[1]
}

func (x ClientLibraryDestination) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClientLibraryDestination.Descriptor instead.
func (ClientLibraryDestination) EnumDescriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{1}
}

// Required information for every language.
type CommonLanguageSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Link to automatically generated reference documentation.  Example:
	// https://cloud.google.com/nodejs/docs/reference/asset/latest
	//
	// Deprecated: Do not use.
	ReferenceDocsUri string `protobuf:"bytes,1,opt,name=reference_docs_uri,json=referenceDocsUri,proto3" json:"reference_docs_uri,omitempty"`
	// The destination where API teams want this client library to be published.
	Destinations []ClientLibraryDestination `protobuf:"varint,2,rep,packed,name=destinations,proto3,enum=google.api.ClientLibraryDestination" json:"destinations,omitempty"`
}

func (x *CommonLanguageSettings) Reset() {
	*x = CommonLanguageSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonLanguageSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonLanguageSettings) ProtoMessage() {}

func (x *CommonLanguageSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonLanguageSettings.ProtoReflect.Descriptor instead.
func (*CommonLanguageSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{0}
}

// Deprecated: Do not use.
func (x *CommonLanguageSettings) GetReferenceDocsUri() string {
	if x != nil {
		return x.ReferenceDocsUri
	}
	return ""
}

func (x *CommonLanguageSettings) GetDestinations() []ClientLibraryDestination {
	if x != nil {
		return x.Destinations
	}
	return nil
}

// Details about how and where to publish client libraries.
type ClientLibrarySettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Version of the API to apply these settings to. This is the full protobuf
	// package for the API, ending in the version element.
	// Examples: "google.cloud.speech.v1" and "google.spanner.admin.database.v1".
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	// Launch stage of this version of the API.
	LaunchStage api.LaunchStage `protobuf:"varint,2,opt,name=launch_stage,json=launchStage,proto3,enum=google.api.LaunchStage" json:"launch_stage,omitempty"`
	// When using transport=rest, the client request will encode enums as
	// numbers rather than strings.
	RestNumericEnums bool `protobuf:"varint,3,opt,name=rest_numeric_enums,json=restNumericEnums,proto3" json:"rest_numeric_enums,omitempty"`
	// Settings for legacy Java features, supported in the Service YAML.
	JavaSettings *JavaSettings `protobuf:"bytes,21,opt,name=java_settings,json=javaSettings,proto3" json:"java_settings,omitempty"`
	// Settings for C++ client libraries.
	CppSettings *CppSettings `protobuf:"bytes,22,opt,name=cpp_settings,json=cppSettings,proto3" json:"cpp_settings,omitempty"`
	// Settings for PHP client libraries.
	PhpSettings *PhpSettings `protobuf:"bytes,23,opt,name=php_settings,json=phpSettings,proto3" json:"php_settings,omitempty"`
	// Settings for Python client libraries.
	PythonSettings *PythonSettings `protobuf:"bytes,24,opt,name=python_settings,json=pythonSettings,proto3" json:"python_settings,omitempty"`
	// Settings for Node client libraries.
	NodeSettings *NodeSettings `protobuf:"bytes,25,opt,name=node_settings,json=nodeSettings,proto3" json:"node_settings,omitempty"`
	// Settings for .NET client libraries.
	DotnetSettings *DotnetSettings `protobuf:"bytes,26,opt,name=dotnet_settings,json=dotnetSettings,proto3" json:"dotnet_settings,omitempty"`
	// Settings for Ruby client libraries.
	RubySettings *RubySettings `protobuf:"bytes,27,opt,name=ruby_settings,json=rubySettings,proto3" json:"ruby_settings,omitempty"`
	// Settings for Go client libraries.
	GoSettings *GoSettings `protobuf:"bytes,28,opt,name=go_settings,json=goSettings,proto3" json:"go_settings,omitempty"`
}

func (x *ClientLibrarySettings) Reset() {
	*x = ClientLibrarySettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClientLibrarySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClientLibrarySettings) ProtoMessage() {}

func (x *ClientLibrarySettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClientLibrarySettings.ProtoReflect.Descriptor instead.
func (*ClientLibrarySettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{1}
}

func (x *ClientLibrarySettings) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *ClientLibrarySettings) GetLaunchStage() api.LaunchStage {
	if x != nil {
		return x.LaunchStage
	}
	return api.LaunchStage_LAUNCH_STAGE_UNSPECIFIED
}

func (x *ClientLibrarySettings) GetRestNumericEnums() bool {
	if x != nil {
		return x.RestNumericEnums
	}
	return false
}

func (x *ClientLibrarySettings) GetJavaSettings() *JavaSettings {
	if x != nil {
		return x.JavaSettings
	}
	return nil
}

func (x *ClientLibrarySettings) GetCppSettings() *CppSettings {
	if x != nil {
		return x.CppSettings
	}
	return nil
}

func (x *ClientLibrarySettings) GetPhpSettings() *PhpSettings {
	if x != nil {
		return x.PhpSettings
	}
	return nil
}

func (x *ClientLibrarySettings) GetPythonSettings() *PythonSettings {
	if x != nil {
		return x.PythonSettings
	}
	return nil
}

func (x *ClientLibrarySettings) GetNodeSettings() *NodeSettings {
	if x != nil {
		return x.NodeSettings
	}
	return nil
}

func (x *ClientLibrarySettings) GetDotnetSettings() *DotnetSettings {
	if x != nil {
		return x.DotnetSettings
	}
	return nil
}

func (x *ClientLibrarySettings) GetRubySettings() *RubySettings {
	if x != nil {
		return x.RubySettings
	}
	return nil
}

func (x *ClientLibrarySettings) GetGoSettings() *GoSettings {
	if x != nil {
		return x.GoSettings
	}
	return nil
}

// This message configures the settings for publishing [Google Cloud Client
// libraries](https://cloud.google.com/apis/docs/cloud-client-libraries)
// generated from the service config.
type Publishing struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// A list of API method settings, e.g. the behavior for methods that use the
	// long-running operation pattern.
	MethodSettings []*MethodSettings `protobuf:"bytes,2,rep,name=method_settings,json=methodSettings,proto3" json:"method_settings,omitempty"`
	// Link to a *public* URI where users can report issues.  Example:
	// https://issuetracker.google.com/issues/new?component=190865&template=1161103
	NewIssueUri string `protobuf:"bytes,101,opt,name=new_issue_uri,json=newIssueUri,proto3" json:"new_issue_uri,omitempty"`
	// Link to product home page.  Example:
	// https://cloud.google.com/asset-inventory/docs/overview
	DocumentationUri string `protobuf:"bytes,102,opt,name=documentation_uri,json=documentationUri,proto3" json:"documentation_uri,omitempty"`
	// Used as a tracking tag when collecting data about the APIs developer
	// relations artifacts like docs, packages delivered to package managers,
	// etc.  Example: "speech".
	ApiShortName string `protobuf:"bytes,103,opt,name=api_short_name,json=apiShortName,proto3" json:"api_short_name,omitempty"`
	// GitHub label to apply to issues and pull requests opened for this API.
	GithubLabel string `protobuf:"bytes,104,opt,name=github_label,json=githubLabel,proto3" json:"github_label,omitempty"`
	// GitHub teams to be added to CODEOWNERS in the directory in GitHub
	// containing source code for the client libraries for this API.
	CodeownerGithubTeams []string `protobuf:"bytes,105,rep,name=codeowner_github_teams,json=codeownerGithubTeams,proto3" json:"codeowner_github_teams,omitempty"`
	// A prefix used in sample code when demarking regions to be included in
	// documentation.
	DocTagPrefix string `protobuf:"bytes,106,opt,name=doc_tag_prefix,json=docTagPrefix,proto3" json:"doc_tag_prefix,omitempty"`
	// For whom the client library is being published.
	Organization ClientLibraryOrganization `protobuf:"varint,107,opt,name=organization,proto3,enum=google.api.ClientLibraryOrganization" json:"organization,omitempty"`
	// Client library settings.  If the same version string appears multiple
	// times in this list, then the last one wins.  Settings from earlier
	// settings with the same version string are discarded.
	LibrarySettings []*ClientLibrarySettings `protobuf:"bytes,109,rep,name=library_settings,json=librarySettings,proto3" json:"library_settings,omitempty"`
	// Optional link to proto reference documentation.  Example:
	// https://cloud.google.com/pubsub/lite/docs/reference/rpc
	ProtoReferenceDocumentationUri string `protobuf:"bytes,110,opt,name=proto_reference_documentation_uri,json=protoReferenceDocumentationUri,proto3" json:"proto_reference_documentation_uri,omitempty"`
	// Optional link to REST reference documentation.  Example:
	// https://cloud.google.com/pubsub/lite/docs/reference/rest
	RestReferenceDocumentationUri string `protobuf:"bytes,111,opt,name=rest_reference_documentation_uri,json=restReferenceDocumentationUri,proto3" json:"rest_reference_documentation_uri,omitempty"`
}

func (x *Publishing) Reset() {
	*x = Publishing{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Publishing) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Publishing) ProtoMessage() {}

func (x *Publishing) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Publishing.ProtoReflect.Descriptor instead.
func (*Publishing) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{2}
}

func (x *Publishing) GetMethodSettings() []*MethodSettings {
	if x != nil {
		return x.MethodSettings
	}
	return nil
}

func (x *Publishing) GetNewIssueUri() string {
	if x != nil {
		return x.NewIssueUri
	}
	return ""
}

func (x *Publishing) GetDocumentationUri() string {
	if x != nil {
		return x.DocumentationUri
	}
	return ""
}

func (x *Publishing) GetApiShortName() string {
	if x != nil {
		return x.ApiShortName
	}
	return ""
}

func (x *Publishing) GetGithubLabel() string {
	if x != nil {
		return x.GithubLabel
	}
	return ""
}

func (x *Publishing) GetCodeownerGithubTeams() []string {
	if x != nil {
		return x.CodeownerGithubTeams
	}
	return nil
}

func (x *Publishing) GetDocTagPrefix() string {
	if x != nil {
		return x.DocTagPrefix
	}
	return ""
}

func (x *Publishing) GetOrganization() ClientLibraryOrganization {
	if x != nil {
		return x.Organization
	}
	return ClientLibraryOrganization_CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED
}

func (x *Publishing) GetLibrarySettings() []*ClientLibrarySettings {
	if x != nil {
		return x.LibrarySettings
	}
	return nil
}

func (x *Publishing) GetProtoReferenceDocumentationUri() string {
	if x != nil {
		return x.ProtoReferenceDocumentationUri
	}
	return ""
}

func (x *Publishing) GetRestReferenceDocumentationUri() string {
	if x != nil {
		return x.RestReferenceDocumentationUri
	}
	return ""
}

// Settings for Java client libraries.
type JavaSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The package name to use in Java. Clobbers the java_package option
	// set in the protobuf. This should be used **only** by APIs
	// who have already set the language_settings.java.package_name" field
	// in gapic.yaml. API teams should use the protobuf java_package option
	// where possible.
	//
	// Example of a YAML configuration::
	//
	//	publishing:
	//	  java_settings:
	//	    library_package: com.google.cloud.pubsub.v1
	LibraryPackage string `protobuf:"bytes,1,opt,name=library_package,json=libraryPackage,proto3" json:"library_package,omitempty"`
	// Configure the Java class name to use instead of the service's for its
	// corresponding generated GAPIC client. Keys are fully-qualified
	// service names as they appear in the protobuf (including the full
	// the language_settings.java.interface_names" field in gapic.yaml. API
	// teams should otherwise use the service name as it appears in the
	// protobuf.
	//
	// Example of a YAML configuration::
	//
	//	publishing:
	//	  java_settings:
	//	    service_class_names:
	//	      - google.pubsub.v1.Publisher: TopicAdmin
	//	      - google.pubsub.v1.Subscriber: SubscriptionAdmin
	ServiceClassNames map[string]string `protobuf:"bytes,2,rep,name=service_class_names,json=serviceClassNames,proto3" json:"service_class_names,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,3,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *JavaSettings) Reset() {
	*x = JavaSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JavaSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JavaSettings) ProtoMessage() {}

func (x *JavaSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JavaSettings.ProtoReflect.Descriptor instead.
func (*JavaSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{3}
}

func (x *JavaSettings) GetLibraryPackage() string {
	if x != nil {
		return x.LibraryPackage
	}
	return ""
}

func (x *JavaSettings) GetServiceClassNames() map[string]string {
	if x != nil {
		return x.ServiceClassNames
	}
	return nil
}

func (x *JavaSettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

// Settings for C++ client libraries.
type CppSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *CppSettings) Reset() {
	*x = CppSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CppSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CppSettings) ProtoMessage() {}

func (x *CppSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CppSettings.ProtoReflect.Descriptor instead.
func (*CppSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{4}
}

func (x *CppSettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

// Settings for Php client libraries.
type PhpSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *PhpSettings) Reset() {
	*x = PhpSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhpSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhpSettings) ProtoMessage() {}

func (x *PhpSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhpSettings.ProtoReflect.Descriptor instead.
func (*PhpSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{5}
}

func (x *PhpSettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

// Settings for Python client libraries.
type PythonSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	// Experimental features to be included during client library generation.
	ExperimentalFeatures *PythonSettings_ExperimentalFeatures `protobuf:"bytes,2,opt,name=experimental_features,json=experimentalFeatures,proto3" json:"experimental_features,omitempty"`
}

func (x *PythonSettings) Reset() {
	*x = PythonSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PythonSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PythonSettings) ProtoMessage() {}

func (x *PythonSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PythonSettings.ProtoReflect.Descriptor instead.
func (*PythonSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{6}
}

func (x *PythonSettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *PythonSettings) GetExperimentalFeatures() *PythonSettings_ExperimentalFeatures {
	if x != nil {
		return x.ExperimentalFeatures
	}
	return nil
}

// Settings for Node client libraries.
type NodeSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *NodeSettings) Reset() {
	*x = NodeSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSettings) ProtoMessage() {}

func (x *NodeSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSettings.ProtoReflect.Descriptor instead.
func (*NodeSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{7}
}

func (x *NodeSettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

// Settings for Dotnet client libraries.
type DotnetSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
	// Map from original service names to renamed versions.
	// This is used when the default generated types
	// would cause a naming conflict. (Neither name is
	// fully-qualified.)
	// Example: Subscriber to SubscriberServiceApi.
	RenamedServices map[string]string `protobuf:"bytes,2,rep,name=renamed_services,json=renamedServices,proto3" json:"renamed_services,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Map from full resource types to the effective short name
	// for the resource. This is used when otherwise resource
	// named from different services would cause naming collisions.
	// Example entry:
	// "datalabeling.googleapis.com/Dataset": "DataLabelingDataset"
	RenamedResources map[string]string `protobuf:"bytes,3,rep,name=renamed_resources,json=renamedResources,proto3" json:"renamed_resources,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// List of full resource types to ignore during generation.
	// This is typically used for API-specific Location resources,
	// which should be handled by the generator as if they were actually
	// the common Location resources.
	// Example entry: "documentai.googleapis.com/Location"
	IgnoredResources []string `protobuf:"bytes,4,rep,name=ignored_resources,json=ignoredResources,proto3" json:"ignored_resources,omitempty"`
	// Namespaces which must be aliased in snippets due to
	// a known (but non-generator-predictable) naming collision
	ForcedNamespaceAliases []string `protobuf:"bytes,5,rep,name=forced_namespace_aliases,json=forcedNamespaceAliases,proto3" json:"forced_namespace_aliases,omitempty"`
	// Method signatures (in the form "service.method(signature)")
	// which are provided separately, so shouldn't be generated.
	// Snippets *calling* these methods are still generated, however.
	HandwrittenSignatures []string `protobuf:"bytes,6,rep,name=handwritten_signatures,json=handwrittenSignatures,proto3" json:"handwritten_signatures,omitempty"`
}

func (x *DotnetSettings) Reset() {
	*x = DotnetSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DotnetSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DotnetSettings) ProtoMessage() {}

func (x *DotnetSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DotnetSettings.ProtoReflect.Descriptor instead.
func (*DotnetSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{8}
}

func (x *DotnetSettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

func (x *DotnetSettings) GetRenamedServices() map[string]string {
	if x != nil {
		return x.RenamedServices
	}
	return nil
}

func (x *DotnetSettings) GetRenamedResources() map[string]string {
	if x != nil {
		return x.RenamedResources
	}
	return nil
}

func (x *DotnetSettings) GetIgnoredResources() []string {
	if x != nil {
		return x.IgnoredResources
	}
	return nil
}

func (x *DotnetSettings) GetForcedNamespaceAliases() []string {
	if x != nil {
		return x.ForcedNamespaceAliases
	}
	return nil
}

func (x *DotnetSettings) GetHandwrittenSignatures() []string {
	if x != nil {
		return x.HandwrittenSignatures
	}
	return nil
}

// Settings for Ruby client libraries.
type RubySettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *RubySettings) Reset() {
	*x = RubySettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RubySettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RubySettings) ProtoMessage() {}

func (x *RubySettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RubySettings.ProtoReflect.Descriptor instead.
func (*RubySettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{9}
}

func (x *RubySettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

// Settings for Go client libraries.
type GoSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Some settings.
	Common *CommonLanguageSettings `protobuf:"bytes,1,opt,name=common,proto3" json:"common,omitempty"`
}

func (x *GoSettings) Reset() {
	*x = GoSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GoSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoSettings) ProtoMessage() {}

func (x *GoSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoSettings.ProtoReflect.Descriptor instead.
func (*GoSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{10}
}

func (x *GoSettings) GetCommon() *CommonLanguageSettings {
	if x != nil {
		return x.Common
	}
	return nil
}

// Describes the generator configuration for a method.
type MethodSettings struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The fully qualified name of the method, for which the options below apply.
	// This is used to find the method to apply the options.
	//
	// Example:
	//
	//	publishing:
	//	  method_settings:
	//	  - selector: google.storage.control.v2.StorageControl.CreateFolder
	//	    # method settings for CreateFolder...
	Selector string `protobuf:"bytes,1,opt,name=selector,proto3" json:"selector,omitempty"`
	// Describes settings to use for long-running operations when generating
	// API methods for RPCs. Complements RPCs that use the annotations in
	// google/longrunning/operations.proto.
	//
	// Example of a YAML configuration::
	//
	//	publishing:
	//	  method_settings:
	//	  - selector: google.cloud.speech.v2.Speech.BatchRecognize
	//	    long_running:
	//	      initial_poll_delay: 60s # 1 minute
	//	      poll_delay_multiplier: 1.5
	//	      max_poll_delay: 360s # 6 minutes
	//	      total_poll_timeout: 54000s # 90 minutes
	LongRunning *MethodSettings_LongRunning `protobuf:"bytes,2,opt,name=long_running,json=longRunning,proto3" json:"long_running,omitempty"`
	// List of top-level fields of the request message, that should be
	// automatically populated by the client libraries based on their
	// (google.api.field_info).format. Currently supported format: UUID4.
	//
	// Example of a YAML configuration:
	//
	//	publishing:
	//	  method_settings:
	//	  - selector: google.example.v1.ExampleService.CreateExample
	//	    auto_populated_fields:
	//	    - request_id
	AutoPopulatedFields []string `protobuf:"bytes,3,rep,name=auto_populated_fields,json=autoPopulatedFields,proto3" json:"auto_populated_fields,omitempty"`
}

func (x *MethodSettings) Reset() {
	*x = MethodSettings{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MethodSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MethodSettings) ProtoMessage() {}

func (x *MethodSettings) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MethodSettings.ProtoReflect.Descriptor instead.
func (*MethodSettings) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{11}
}

func (x *MethodSettings) GetSelector() string {
	if x != nil {
		return x.Selector
	}
	return ""
}

func (x *MethodSettings) GetLongRunning() *MethodSettings_LongRunning {
	if x != nil {
		return x.LongRunning
	}
	return nil
}

func (x *MethodSettings) GetAutoPopulatedFields() []string {
	if x != nil {
		return x.AutoPopulatedFields
	}
	return nil
}

// Experimental features to be included during client library generation.
// These fields will be deprecated once the feature graduates and is enabled
// by default.
type PythonSettings_ExperimentalFeatures struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Enables generation of asynchronous REST clients if `rest` transport is
	// enabled. By default, asynchronous REST clients will not be generated.
	// This feature will be enabled by default 1 month after launching the
	// feature in preview packages.
	RestAsyncIoEnabled bool `protobuf:"varint,1,opt,name=rest_async_io_enabled,json=restAsyncIoEnabled,proto3" json:"rest_async_io_enabled,omitempty"`
}

func (x *PythonSettings_ExperimentalFeatures) Reset() {
	*x = PythonSettings_ExperimentalFeatures{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PythonSettings_ExperimentalFeatures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PythonSettings_ExperimentalFeatures) ProtoMessage() {}

func (x *PythonSettings_ExperimentalFeatures) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PythonSettings_ExperimentalFeatures.ProtoReflect.Descriptor instead.
func (*PythonSettings_ExperimentalFeatures) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{6, 0}
}

func (x *PythonSettings_ExperimentalFeatures) GetRestAsyncIoEnabled() bool {
	if x != nil {
		return x.RestAsyncIoEnabled
	}
	return false
}

// Describes settings to use when generating API methods that use the
// long-running operation pattern.
// All default values below are from those used in the client library
// generators (e.g.
// [Java](https://github.com/googleapis/gapic-generator-java/blob/04c2faa191a9b5a10b92392fe8482279c4404803/src/main/java/com/google/api/generator/gapic/composer/common/RetrySettingsComposer.java)).
type MethodSettings_LongRunning struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Initial delay after which the first poll request will be made.
	// Default value: 5 seconds.
	InitialPollDelay *durationpb.Duration `protobuf:"bytes,1,opt,name=initial_poll_delay,json=initialPollDelay,proto3" json:"initial_poll_delay,omitempty"`
	// Multiplier to gradually increase delay between subsequent polls until it
	// reaches max_poll_delay.
	// Default value: 1.5.
	PollDelayMultiplier float32 `protobuf:"fixed32,2,opt,name=poll_delay_multiplier,json=pollDelayMultiplier,proto3" json:"poll_delay_multiplier,omitempty"`
	// Maximum time between two subsequent poll requests.
	// Default value: 45 seconds.
	MaxPollDelay *durationpb.Duration `protobuf:"bytes,3,opt,name=max_poll_delay,json=maxPollDelay,proto3" json:"max_poll_delay,omitempty"`
	// Total polling timeout.
	// Default value: 5 minutes.
	TotalPollTimeout *durationpb.Duration `protobuf:"bytes,4,opt,name=total_poll_timeout,json=totalPollTimeout,proto3" json:"total_poll_timeout,omitempty"`
}

func (x *MethodSettings_LongRunning) Reset() {
	*x = MethodSettings_LongRunning{}
	if protoimpl.UnsafeEnabled {
		mi := &file_google_api_client_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *MethodSettings_LongRunning) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MethodSettings_LongRunning) ProtoMessage() {}

func (x *MethodSettings_LongRunning) ProtoReflect() protoreflect.Message {
	mi := &file_google_api_client_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MethodSettings_LongRunning.ProtoReflect.Descriptor instead.
func (*MethodSettings_LongRunning) Descriptor() ([]byte, []int) {
	return file_google_api_client_proto_rawDescGZIP(), []int{11, 0}
}

func (x *MethodSettings_LongRunning) GetInitialPollDelay() *durationpb.Duration {
	if x != nil {
		return x.InitialPollDelay
	}
	return nil
}

func (x *MethodSettings_LongRunning) GetPollDelayMultiplier() float32 {
	if x != nil {
		return x.PollDelayMultiplier
	}
	return 0
}

func (x *MethodSettings_LongRunning) GetMaxPollDelay() *durationpb.Duration {
	if x != nil {
		return x.MaxPollDelay
	}
	return nil
}

func (x *MethodSettings_LongRunning) GetTotalPollTimeout() *durationpb.Duration {
	if x != nil {
		return x.TotalPollTimeout
	}
	return nil
}

var file_google_api_client_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.MethodOptions)(nil),
		ExtensionType: ([]string)(nil),
		Field:         1051,
		Name:          "google.api.method_signature",
		Tag:           "bytes,1051,rep,name=method_signature",
		Filename:      "google/api/client.proto",
	},
	{
		ExtendedType:  (*descriptorpb.ServiceOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         1049,
		Name:          "google.api.default_host",
		Tag:           "bytes,1049,opt,name=default_host",
		Filename:      "google/api/client.proto",
	},
	{
		ExtendedType:  (*descriptorpb.ServiceOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         1050,
		Name:          "google.api.oauth_scopes",
		Tag:           "bytes,1050,opt,name=oauth_scopes",
		Filename:      "google/api/client.proto",
	},
	{
		ExtendedType:  (*descriptorpb.ServiceOptions)(nil),
		ExtensionType: (*string)(nil),
		Field:         525000001,
		Name:          "google.api.api_version",
		Tag:           "bytes,525000001,opt,name=api_version",
		Filename:      "google/api/client.proto",
	},
}

// Extension fields to descriptorpb.MethodOptions.
var (
	// A definition of a client library method signature.
	//
	// In client libraries, each proto RPC corresponds to one or more methods
	// which the end user is able to call, and calls the underlying RPC.
	// Normally, this method receives a single argument (a struct or instance
	// corresponding to the RPC request object). Defining this field will
	// add one or more overloads providing flattened or simpler method signatures
	// in some languages.
	//
	// The fields on the method signature are provided as a comma-separated
	// string.
	//
	// For example, the proto RPC and annotation:
	//
	//	rpc CreateSubscription(CreateSubscriptionRequest)
	//	    returns (Subscription) {
	//	  option (google.api.method_signature) = "name,topic";
	//	}
	//
	// Would add the following Java overload (in addition to the method accepting
	// the request object):
	//
	//	public final Subscription createSubscription(String name, String topic)
	//
	// The following backwards-compatibility guidelines apply:
	//
	//   - Adding this annotation to an unannotated method is backwards
	//     compatible.
	//   - Adding this annotation to a method which already has existing
	//     method signature annotations is backwards compatible if and only if
	//     the new method signature annotation is last in the sequence.
	//   - Modifying or removing an existing method signature annotation is
	//     a breaking change.
	//   - Re-ordering existing method signature annotations is a breaking
	//     change.
	//
	// repeated string method_signature = 1051;
	E_MethodSignature = &file_google_api_client_proto_extTypes[0]
)

// Extension fields to descriptorpb.ServiceOptions.
var (
	// The hostname for this service.
	// This should be specified with no prefix or protocol.
	//
	// Example:
	//
	//	service Foo {
	//	  option (google.api.default_host) = "foo.googleapi.com";
	//	  ...
	//	}
	//
	// optional string default_host = 1049;
	E_DefaultHost = &file_google_api_client_proto_extTypes[1]
	// OAuth scopes needed for the client.
	//
	// Example:
	//
	//	service Foo {
	//	  option (google.api.oauth_scopes) = \
	//	    "https://www.googleapis.com/auth/cloud-platform";
	//	  ...
	//	}
	//
	// If there is more than one scope, use a comma-separated string:
	//
	// Example:
	//
	//	service Foo {
	//	  option (google.api.oauth_scopes) = \
	//	    "https://www.googleapis.com/auth/cloud-platform,"
	//	    "https://www.googleapis.com/auth/monitoring";
	//	  ...
	//	}
	//
	// optional string oauth_scopes = 1050;
	E_OauthScopes = &file_google_api_client_proto_extTypes[2]
	// The API version of this service, which should be sent by version-aware
	// clients to the service. This allows services to abide by the schema and
	// behavior of the service at the time this API version was deployed.
	// The format of the API version must be treated as opaque by clients.
	// Services may use a format with an apparent structure, but clients must
	// not rely on this to determine components within an API version, or attempt
	// to construct other valid API versions. Note that this is for upcoming
	// functionality and may not be implemented for all services.
	//
	// Example:
	//
	//	service Foo {
	//	  option (google.api.api_version) = "v1_20230821_preview";
	//	}
	//
	// optional string api_version = 525000001;
	E_ApiVersion = &file_google_api_client_proto_extTypes[3]
)

var File_google_api_client_proto protoreflect.FileDescriptor

var file_google_api_client_proto_rawDesc = []byte{
	0x0a, 0x17, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x63, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x61, 0x70, 0x69, 0x1a, 0x1d, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x67, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x6f, 0x72,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x94, 0x01, 0x0a, 0x16, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x30, 0x0a, 0x12, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x64,
	0x6f, 0x63, 0x73, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18,
	0x01, 0x52, 0x10, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x73,
	0x55, 0x72, 0x69, 0x12, 0x48, 0x0a, 0x0c, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62,
	0x72, 0x61, 0x72, 0x79, 0x44, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0c, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x93, 0x05,
	0x0a, 0x15, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x3a, 0x0a, 0x0c, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x5f, 0x73, 0x74, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65,
	0x52, 0x0b, 0x6c, 0x61, 0x75, 0x6e, 0x63, 0x68, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x6e, 0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x5f, 0x65, 0x6e,
	0x75, 0x6d, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x72, 0x65, 0x73, 0x74, 0x4e,
	0x75, 0x6d, 0x65, 0x72, 0x69, 0x63, 0x45, 0x6e, 0x75, 0x6d, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x6a,
	0x61, 0x76, 0x61, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x15, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x4a, 0x61, 0x76, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0c, 0x6a, 0x61,
	0x76, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x63, 0x70,
	0x70, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x16, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x70,
	0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0b, 0x63, 0x70, 0x70, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x68, 0x70, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x17, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x68, 0x70, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0b, 0x70, 0x68, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e,
	0x67, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x70, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x18, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0e, 0x70, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x19, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4e, 0x6f, 0x64, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x64, 0x6f, 0x74, 0x6e, 0x65, 0x74,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x1a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x6f, 0x74,
	0x6e, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0e, 0x64, 0x6f, 0x74,
	0x6e, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3d, 0x0a, 0x0d, 0x72,
	0x75, 0x62, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x1b, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x52, 0x75, 0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0c, 0x72, 0x75,
	0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x37, 0x0a, 0x0b, 0x67, 0x6f,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x1c, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x47, 0x6f, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0a, 0x67, 0x6f, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x22, 0xf4, 0x04, 0x0a, 0x0a, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x69,
	0x6e, 0x67, 0x12, 0x43, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0e, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x22, 0x0a, 0x0d, 0x6e, 0x65, 0x77, 0x5f, 0x69,
	0x73, 0x73, 0x75, 0x65, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x65, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6e, 0x65, 0x77, 0x49, 0x73, 0x73, 0x75, 0x65, 0x55, 0x72, 0x69, 0x12, 0x2b, 0x0a, 0x11, 0x64,
	0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x69,
	0x18, 0x66, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x69, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x70, 0x69, 0x5f,
	0x73, 0x68, 0x6f, 0x72, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x67, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x61, 0x70, 0x69, 0x53, 0x68, 0x6f, 0x72, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x68,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x12, 0x34, 0x0a, 0x16, 0x63, 0x6f, 0x64, 0x65, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x5f, 0x74, 0x65, 0x61, 0x6d, 0x73, 0x18, 0x69, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x14, 0x63, 0x6f, 0x64, 0x65, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x47, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x54, 0x65, 0x61, 0x6d, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x6f, 0x63, 0x5f, 0x74,
	0x61, 0x67, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x69, 0x78, 0x18, 0x6a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x6f, 0x63, 0x54, 0x61, 0x67, 0x50, 0x72, 0x65, 0x66, 0x69, 0x78, 0x12, 0x49, 0x0a,
	0x0c, 0x6f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x6b, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x4f, 0x72,
	0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x6f, 0x72, 0x67, 0x61,
	0x6e, 0x69, 0x7a, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x10, 0x6c, 0x69, 0x62, 0x72,
	0x61, 0x72, 0x79, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x6d, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x21, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x0f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x49, 0x0a, 0x21, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x5f,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x6e, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x1e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72,
	0x69, 0x12, 0x47, 0x0a, 0x20, 0x72, 0x65, 0x73, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x65, 0x72, 0x65,
	0x6e, 0x63, 0x65, 0x5f, 0x64, 0x6f, 0x63, 0x75, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x75, 0x72, 0x69, 0x18, 0x6f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1d, 0x72, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x44, 0x6f, 0x63, 0x75, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x72, 0x69, 0x22, 0x9a, 0x02, 0x0a, 0x0c, 0x4a,
	0x61, 0x76, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x12, 0x5f, 0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f,
	0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x4a,
	0x61, 0x76, 0x61, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67,
	0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x1a, 0x44, 0x0a, 0x16, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x61, 0x73,
	0x73, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x49, 0x0a, 0x0b, 0x43, 0x70, 0x70, 0x53, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61,
	0x67, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x22, 0x49, 0x0a, 0x0b, 0x50, 0x68, 0x70, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x12, 0x3a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xfd, 0x01,
	0x0a, 0x0e, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x12, 0x3a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x64, 0x0a, 0x15,
	0x65, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x5f, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x50, 0x79, 0x74, 0x68, 0x6f, 0x6e, 0x53,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x6c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x14, 0x65, 0x78,
	0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x6c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x73, 0x1a, 0x49, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x65, 0x72, 0x69, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x6c, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x31, 0x0a, 0x15, 0x72, 0x65,
	0x73, 0x74, 0x5f, 0x61, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x69, 0x6f, 0x5f, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x72, 0x65, 0x73, 0x74, 0x41,
	0x73, 0x79, 0x6e, 0x63, 0x49, 0x6f, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x4a, 0x0a,
	0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3a, 0x0a,
	0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0xae, 0x04, 0x0a, 0x0e, 0x44, 0x6f,
	0x74, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3a, 0x0a, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73,
	0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x5a, 0x0a, 0x10, 0x72, 0x65, 0x6e, 0x61,
	0x6d, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x44, 0x6f, 0x74, 0x6e, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x52,
	0x65, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0f, 0x72, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x12, 0x5d, 0x0a, 0x11, 0x72, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x5f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x44, 0x6f, 0x74,
	0x6e, 0x65, 0x74, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x2e, 0x52, 0x65, 0x6e, 0x61,
	0x6d, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x10, 0x72, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x5f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10,
	0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73,
	0x12, 0x38, 0x0a, 0x18, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x5f, 0x61, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x16, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x41, 0x6c, 0x69, 0x61, 0x73, 0x65, 0x73, 0x12, 0x35, 0x0a, 0x16, 0x68, 0x61,
	0x6e, 0x64, 0x77, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x15, 0x68, 0x61, 0x6e, 0x64,
	0x77, 0x72, 0x69, 0x74, 0x74, 0x65, 0x6e, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x1a, 0x42, 0x0a, 0x14, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x64, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x43, 0x0a, 0x15, 0x52, 0x65, 0x6e, 0x61, 0x6d, 0x65, 0x64,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x4a, 0x0a, 0x0c, 0x52, 0x75,
	0x62, 0x79, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x61,
	0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x06,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x22, 0x48, 0x0a, 0x0a, 0x47, 0x6f, 0x53, 0x65, 0x74, 0x74,
	0x69, 0x6e, 0x67, 0x73, 0x12, 0x3a, 0x0a, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65,
	0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x06, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x22, 0xc2, 0x03, 0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69,
	0x6e, 0x67, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12,
	0x49, 0x0a, 0x0c, 0x6c, 0x6f, 0x6e, 0x67, 0x5f, 0x72, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x73, 0x2e, 0x4c, 0x6f, 0x6e, 0x67, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x52, 0x0b, 0x6c,
	0x6f, 0x6e, 0x67, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x32, 0x0a, 0x15, 0x61, 0x75,
	0x74, 0x6f, 0x5f, 0x70, 0x6f, 0x70, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x66, 0x69, 0x65,
	0x6c, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x13, 0x61, 0x75, 0x74, 0x6f, 0x50,
	0x6f, 0x70, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x64, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x1a, 0x94,
	0x02, 0x0a, 0x0b, 0x4c, 0x6f, 0x6e, 0x67, 0x52, 0x75, 0x6e, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x47,
	0x0a, 0x12, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x64,
	0x65, 0x6c, 0x61, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x10, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x61, 0x6c, 0x50, 0x6f,
	0x6c, 0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x6f, 0x6c, 0x6c, 0x5f,
	0x64, 0x65, 0x6c, 0x61, 0x79, 0x5f, 0x6d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x02, 0x52, 0x13, 0x70, 0x6f, 0x6c, 0x6c, 0x44, 0x65, 0x6c, 0x61,
	0x79, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x69, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0e, 0x6d,
	0x61, 0x78, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x64, 0x65, 0x6c, 0x61, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c,
	0x6d, 0x61, 0x78, 0x50, 0x6f, 0x6c, 0x6c, 0x44, 0x65, 0x6c, 0x61, 0x79, 0x12, 0x47, 0x0a, 0x12,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x6c, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x10, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x50, 0x6f, 0x6c, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x2a, 0xa3, 0x01, 0x0a, 0x19, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74,
	0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x4f, 0x72, 0x67, 0x61, 0x6e, 0x69, 0x7a, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x2b, 0x0a, 0x27, 0x43, 0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x4c, 0x49,
	0x42, 0x52, 0x41, 0x52, 0x59, 0x5f, 0x4f, 0x52, 0x47, 0x41, 0x4e, 0x49, 0x5a, 0x41, 0x54, 0x49,
	0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x09, 0x0a, 0x05, 0x43, 0x4c, 0x4f, 0x55, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x41,
	0x44, 0x53, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x48, 0x4f, 0x54, 0x4f, 0x53, 0x10, 0x03,
	0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x54, 0x52, 0x45, 0x45, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x10,
	0x04, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x48, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x05, 0x12,
	0x07, 0x0a, 0x03, 0x47, 0x45, 0x4f, 0x10, 0x06, 0x12, 0x11, 0x0a, 0x0d, 0x47, 0x45, 0x4e, 0x45,
	0x52, 0x41, 0x54, 0x49, 0x56, 0x45, 0x5f, 0x41, 0x49, 0x10, 0x07, 0x2a, 0x67, 0x0a, 0x18, 0x43,
	0x6c, 0x69, 0x65, 0x6e, 0x74, 0x4c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x44, 0x65, 0x73, 0x74,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2a, 0x0a, 0x26, 0x43, 0x4c, 0x49, 0x45, 0x4e,
	0x54, 0x5f, 0x4c, 0x49, 0x42, 0x52, 0x41, 0x52, 0x59, 0x5f, 0x44, 0x45, 0x53, 0x54, 0x49, 0x4e,
	0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x49, 0x54, 0x48, 0x55, 0x42, 0x10, 0x0a, 0x12,
	0x13, 0x0a, 0x0f, 0x50, 0x41, 0x43, 0x4b, 0x41, 0x47, 0x45, 0x5f, 0x4d, 0x41, 0x4e, 0x41, 0x47,
	0x45, 0x52, 0x10, 0x14, 0x3a, 0x4a, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x5f, 0x73,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x1e, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x9b, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0f, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x3a, 0x43, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x68, 0x6f, 0x73, 0x74,
	0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x99, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x48, 0x6f, 0x73, 0x74, 0x3a, 0x43, 0x0a, 0x0c, 0x6f, 0x61, 0x75, 0x74, 0x68, 0x5f, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x73, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x9a, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f,
	0x61, 0x75, 0x74, 0x68, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x73, 0x3a, 0x44, 0x0a, 0x0b, 0x61, 0x70,
	0x69, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0xc1, 0xba, 0xab, 0xfa, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x61, 0x70, 0x69, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x42, 0x69, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x61,
	0x70, 0x69, 0x42, 0x0b, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x50,
	0x01, 0x5a, 0x41, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x67, 0x6f, 0x6c, 0x61, 0x6e, 0x67,
	0x2e, 0x6f, 0x72, 0x67, 0x2f, 0x67, 0x65, 0x6e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x61, 0x70, 0x69, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0xa2, 0x02, 0x04, 0x47, 0x41, 0x50, 0x49, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_google_api_client_proto_rawDescOnce sync.Once
	file_google_api_client_proto_rawDescData = file_google_api_client_proto_rawDesc
)

func file_google_api_client_proto_rawDescGZIP() []byte {
	file_google_api_client_proto_rawDescOnce.Do(func() {
		file_google_api_client_proto_rawDescData = protoimpl.X.CompressGZIP(file_google_api_client_proto_rawDescData)
	})
	return file_google_api_client_proto_rawDescData
}

var file_google_api_client_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_google_api_client_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_google_api_client_proto_goTypes = []interface{}{
	(ClientLibraryOrganization)(0),              // 0: google.api.ClientLibraryOrganization
	(ClientLibraryDestination)(0),               // 1: google.api.ClientLibraryDestination
	(*CommonLanguageSettings)(nil),              // 2: google.api.CommonLanguageSettings
	(*ClientLibrarySettings)(nil),               // 3: google.api.ClientLibrarySettings
	(*Publishing)(nil),                          // 4: google.api.Publishing
	(*JavaSettings)(nil),                        // 5: google.api.JavaSettings
	(*CppSettings)(nil),                         // 6: google.api.CppSettings
	(*PhpSettings)(nil),                         // 7: google.api.PhpSettings
	(*PythonSettings)(nil),                      // 8: google.api.PythonSettings
	(*NodeSettings)(nil),                        // 9: google.api.NodeSettings
	(*DotnetSettings)(nil),                      // 10: google.api.DotnetSettings
	(*RubySettings)(nil),                        // 11: google.api.RubySettings
	(*GoSettings)(nil),                          // 12: google.api.GoSettings
	(*MethodSettings)(nil),                      // 13: google.api.MethodSettings
	nil,                                         // 14: google.api.JavaSettings.ServiceClassNamesEntry
	(*PythonSettings_ExperimentalFeatures)(nil), // 15: google.api.PythonSettings.ExperimentalFeatures
	nil,                                 // 16: google.api.DotnetSettings.RenamedServicesEntry
	nil,                                 // 17: google.api.DotnetSettings.RenamedResourcesEntry
	(*MethodSettings_LongRunning)(nil),  // 18: google.api.MethodSettings.LongRunning
	(api.LaunchStage)(0),                // 19: google.api.LaunchStage
	(*durationpb.Duration)(nil),         // 20: google.protobuf.Duration
	(*descriptorpb.MethodOptions)(nil),  // 21: google.protobuf.MethodOptions
	(*descriptorpb.ServiceOptions)(nil), // 22: google.protobuf.ServiceOptions
}
var file_google_api_client_proto_depIdxs = []int32{
	1,  // 0: google.api.CommonLanguageSettings.destinations:type_name -> google.api.ClientLibraryDestination
	19, // 1: google.api.ClientLibrarySettings.launch_stage:type_name -> google.api.LaunchStage
	5,  // 2: google.api.ClientLibrarySettings.java_settings:type_name -> google.api.JavaSettings
	6,  // 3: google.api.ClientLibrarySettings.cpp_settings:type_name -> google.api.CppSettings
	7,  // 4: google.api.ClientLibrarySettings.php_settings:type_name -> google.api.PhpSettings
	8,  // 5: google.api.ClientLibrarySettings.python_settings:type_name -> google.api.PythonSettings
	9,  // 6: google.api.ClientLibrarySettings.node_settings:type_name -> google.api.NodeSettings
	10, // 7: google.api.ClientLibrarySettings.dotnet_settings:type_name -> google.api.DotnetSettings
	11, // 8: google.api.ClientLibrarySettings.ruby_settings:type_name -> google.api.RubySettings
	12, // 9: google.api.ClientLibrarySettings.go_settings:type_name -> google.api.GoSettings
	13, // 10: google.api.Publishing.method_settings:type_name -> google.api.MethodSettings
	0,  // 11: google.api.Publishing.organization:type_name -> google.api.ClientLibraryOrganization
	3,  // 12: google.api.Publishing.library_settings:type_name -> google.api.ClientLibrarySettings
	14, // 13: google.api.JavaSettings.service_class_names:type_name -> google.api.JavaSettings.ServiceClassNamesEntry
	2,  // 14: google.api.JavaSettings.common:type_name -> google.api.CommonLanguageSettings
	2,  // 15: google.api.CppSettings.common:type_name -> google.api.CommonLanguageSettings
	2,  // 16: google.api.PhpSettings.common:type_name -> google.api.CommonLanguageSettings
	2,  // 17: google.api.PythonSettings.common:type_name -> google.api.CommonLanguageSettings
	15, // 18: google.api.PythonSettings.experimental_features:type_name -> google.api.PythonSettings.ExperimentalFeatures
	2,  // 19: google.api.NodeSettings.common:type_name -> google.api.CommonLanguageSettings
	2,  // 20: google.api.DotnetSettings.common:type_name -> google.api.CommonLanguageSettings
	16, // 21: google.api.DotnetSettings.renamed_services:type_name -> google.api.DotnetSettings.RenamedServicesEntry
	17, // 22: google.api.DotnetSettings.renamed_resources:type_name -> google.api.DotnetSettings.RenamedResourcesEntry
	2,  // 23: google.api.RubySettings.common:type_name -> google.api.CommonLanguageSettings
	2,  // 24: google.api.GoSettings.common:type_name -> google.api.CommonLanguageSettings
	18, // 25: google.api.MethodSettings.long_running:type_name -> google.api.MethodSettings.LongRunning
	20, // 26: google.api.MethodSettings.LongRunning.initial_poll_delay:type_name -> google.protobuf.Duration
	20, // 27: google.api.MethodSettings.LongRunning.max_poll_delay:type_name -> google.protobuf.Duration
	20, // 28: google.api.MethodSettings.LongRunning.total_poll_timeout:type_name -> google.protobuf.Duration
	21, // 29: google.api.method_signature:extendee -> google.protobuf.MethodOptions
	22, // 30: google.api.default_host:extendee -> google.protobuf.ServiceOptions
	22, // 31: google.api.oauth_scopes:extendee -> google.protobuf.ServiceOptions
	22, // 32: google.api.api_version:extendee -> google.protobuf.ServiceOptions
	33, // [33:33] is the sub-list for method output_type
	33, // [33:33] is the sub-list for method input_type
	33, // [33:33] is the sub-list for extension type_name
	29, // [29:33] is the sub-list for extension extendee
	0,  // [0:29] is the sub-list for field type_name
}

func init() { file_google_api_client_proto_init() }
func file_google_api_client_proto_init() {
	if File_google_api_client_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_google_api_client_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonLanguageSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClientLibrarySettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Publishing); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JavaSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CppSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhpSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PythonSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DotnetSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RubySettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GoSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MethodSettings); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PythonSettings_ExperimentalFeatures); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_google_api_client_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*MethodSettings_LongRunning); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_google_api_client_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   17,
			NumExtensions: 4,
			NumServices:   0,
		},
		GoTypes:           file_google_api_client_proto_goTypes,
		DependencyIndexes: file_google_api_client_proto_depIdxs,
		EnumInfos:         file_google_api_client_proto_enumTypes,
		MessageInfos:      file_google_api_client_proto_msgTypes,
		ExtensionInfos:    file_google_api_client_proto_extTypes,
	}.Build()
	File_google_api_client_proto = out.File
	file_google_api_client_proto_rawDesc = nil
	file_google_api_client_proto_goTypes = nil
	file_google_api_client_proto_depIdxs = nil
}
