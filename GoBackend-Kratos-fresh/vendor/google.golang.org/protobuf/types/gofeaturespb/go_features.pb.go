// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

// Code generated by protoc-gen-go. DO NOT EDIT.
// source: google/protobuf/go_features.proto

package gofeaturespb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	descriptorpb "google.golang.org/protobuf/types/descriptorpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

type GoFeatures_APILevel int32

const (
	// API_LEVEL_UNSPECIFIED results in selecting the OPEN API,
	// but needs to be a separate value to distinguish between
	// an explicitly set api level or a missing api level.
	GoFeatures_API_LEVEL_UNSPECIFIED GoFeatures_APILevel = 0
	GoFeatures_API_OPEN              GoFeatures_APILevel = 1
	GoFeatures_API_HYBRID            GoFeatures_APILevel = 2
	GoFeatures_API_OPAQUE            GoFeatures_APILevel = 3
)

// Enum value maps for GoFeatures_APILevel.
var (
	GoFeatures_APILevel_name = map[int32]string{
		0: "API_LEVEL_UNSPECIFIED",
		1: "API_OPEN",
		2: "API_HYBRID",
		3: "API_OPAQUE",
	}
	GoFeatures_APILevel_value = map[string]int32{
		"API_LEVEL_UNSPECIFIED": 0,
		"API_OPEN":              1,
		"API_HYBRID":            2,
		"API_OPAQUE":            3,
	}
)

func (x GoFeatures_APILevel) Enum() *GoFeatures_APILevel {
	p := new(GoFeatures_APILevel)
	*p = x
	return p
}

func (x GoFeatures_APILevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoFeatures_APILevel) Descriptor() protoreflect.EnumDescriptor {
	return file_google_protobuf_go_features_proto_enumTypes[0].Descriptor()
}

func (GoFeatures_APILevel) Type() protoreflect.EnumType {
	return &file_google_protobuf_go_features_proto_enumTypes[0]
}

func (x GoFeatures_APILevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *GoFeatures_APILevel) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = GoFeatures_APILevel(num)
	return nil
}

// Deprecated: Use GoFeatures_APILevel.Descriptor instead.
func (GoFeatures_APILevel) EnumDescriptor() ([]byte, []int) {
	return file_google_protobuf_go_features_proto_rawDescGZIP(), []int{0, 0}
}

type GoFeatures_StripEnumPrefix int32

const (
	GoFeatures_STRIP_ENUM_PREFIX_UNSPECIFIED   GoFeatures_StripEnumPrefix = 0
	GoFeatures_STRIP_ENUM_PREFIX_KEEP          GoFeatures_StripEnumPrefix = 1
	GoFeatures_STRIP_ENUM_PREFIX_GENERATE_BOTH GoFeatures_StripEnumPrefix = 2
	GoFeatures_STRIP_ENUM_PREFIX_STRIP         GoFeatures_StripEnumPrefix = 3
)

// Enum value maps for GoFeatures_StripEnumPrefix.
var (
	GoFeatures_StripEnumPrefix_name = map[int32]string{
		0: "STRIP_ENUM_PREFIX_UNSPECIFIED",
		1: "STRIP_ENUM_PREFIX_KEEP",
		2: "STRIP_ENUM_PREFIX_GENERATE_BOTH",
		3: "STRIP_ENUM_PREFIX_STRIP",
	}
	GoFeatures_StripEnumPrefix_value = map[string]int32{
		"STRIP_ENUM_PREFIX_UNSPECIFIED":   0,
		"STRIP_ENUM_PREFIX_KEEP":          1,
		"STRIP_ENUM_PREFIX_GENERATE_BOTH": 2,
		"STRIP_ENUM_PREFIX_STRIP":         3,
	}
)

func (x GoFeatures_StripEnumPrefix) Enum() *GoFeatures_StripEnumPrefix {
	p := new(GoFeatures_StripEnumPrefix)
	*p = x
	return p
}

func (x GoFeatures_StripEnumPrefix) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GoFeatures_StripEnumPrefix) Descriptor() protoreflect.EnumDescriptor {
	return file_google_protobuf_go_features_proto_enumTypes[1].Descriptor()
}

func (GoFeatures_StripEnumPrefix) Type() protoreflect.EnumType {
	return &file_google_protobuf_go_features_proto_enumTypes[1]
}

func (x GoFeatures_StripEnumPrefix) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Do not use.
func (x *GoFeatures_StripEnumPrefix) UnmarshalJSON(b []byte) error {
	num, err := protoimpl.X.UnmarshalJSONEnum(x.Descriptor(), b)
	if err != nil {
		return err
	}
	*x = GoFeatures_StripEnumPrefix(num)
	return nil
}

// Deprecated: Use GoFeatures_StripEnumPrefix.Descriptor instead.
func (GoFeatures_StripEnumPrefix) EnumDescriptor() ([]byte, []int) {
	return file_google_protobuf_go_features_proto_rawDescGZIP(), []int{0, 1}
}

type GoFeatures struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether or not to generate the deprecated UnmarshalJSON method for enums.
	// Can only be true for proto using the Open Struct api.
	LegacyUnmarshalJsonEnum *bool `protobuf:"varint,1,opt,name=legacy_unmarshal_json_enum,json=legacyUnmarshalJsonEnum" json:"legacy_unmarshal_json_enum,omitempty"`
	// One of OPEN, HYBRID or OPAQUE.
	ApiLevel        *GoFeatures_APILevel        `protobuf:"varint,2,opt,name=api_level,json=apiLevel,enum=pb.GoFeatures_APILevel" json:"api_level,omitempty"`
	StripEnumPrefix *GoFeatures_StripEnumPrefix `protobuf:"varint,3,opt,name=strip_enum_prefix,json=stripEnumPrefix,enum=pb.GoFeatures_StripEnumPrefix" json:"strip_enum_prefix,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GoFeatures) Reset() {
	*x = GoFeatures{}
	mi := &file_google_protobuf_go_features_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoFeatures) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoFeatures) ProtoMessage() {}

func (x *GoFeatures) ProtoReflect() protoreflect.Message {
	mi := &file_google_protobuf_go_features_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoFeatures.ProtoReflect.Descriptor instead.
func (*GoFeatures) Descriptor() ([]byte, []int) {
	return file_google_protobuf_go_features_proto_rawDescGZIP(), []int{0}
}

func (x *GoFeatures) GetLegacyUnmarshalJsonEnum() bool {
	if x != nil && x.LegacyUnmarshalJsonEnum != nil {
		return *x.LegacyUnmarshalJsonEnum
	}
	return false
}

func (x *GoFeatures) GetApiLevel() GoFeatures_APILevel {
	if x != nil && x.ApiLevel != nil {
		return *x.ApiLevel
	}
	return GoFeatures_API_LEVEL_UNSPECIFIED
}

func (x *GoFeatures) GetStripEnumPrefix() GoFeatures_StripEnumPrefix {
	if x != nil && x.StripEnumPrefix != nil {
		return *x.StripEnumPrefix
	}
	return GoFeatures_STRIP_ENUM_PREFIX_UNSPECIFIED
}

var file_google_protobuf_go_features_proto_extTypes = []protoimpl.ExtensionInfo{
	{
		ExtendedType:  (*descriptorpb.FeatureSet)(nil),
		ExtensionType: (*GoFeatures)(nil),
		Field:         1002,
		Name:          "pb.go",
		Tag:           "bytes,1002,opt,name=go",
		Filename:      "google/protobuf/go_features.proto",
	},
}

// Extension fields to descriptorpb.FeatureSet.
var (
	// optional pb.GoFeatures go = 1002;
	E_Go = &file_google_protobuf_go_features_proto_extTypes[0]
)

var File_google_protobuf_go_features_proto protoreflect.FileDescriptor

const file_google_protobuf_go_features_proto_rawDesc = "" +
	"\n" +
	"!google/protobuf/go_features.proto\x12\x02pb\x1a google/protobuf/descriptor.proto\"\xab\x05\n" +
	"\n" +
	"GoFeatures\x12\xbe\x01\n" +
	"\x1alegacy_unmarshal_json_enum\x18\x01 \x01(\bB\x80\x01\x88\x01\x01\x98\x01\x06\x98\x01\x01\xa2\x01\t\x12\x04true\x18\x84\a\xa2\x01\n" +
	"\x12\x05false\x18\xe7\a\xb2\x01[\b\xe8\a\x10\xe8\a\x1aSThe legacy UnmarshalJSON API is deprecated and will be removed in a future edition.R\x17legacyUnmarshalJsonEnum\x12t\n" +
	"\tapi_level\x18\x02 \x01(\x0e2\x17.pb.GoFeatures.APILevelB>\x88\x01\x01\x98\x01\x03\x98\x01\x01\xa2\x01\x1a\x12\x15API_LEVEL_UNSPECIFIED\x18\x84\a\xa2\x01\x0f\x12\n" +
	"API_OPAQUE\x18\xe9\a\xb2\x01\x03\b\xe8\aR\bapiLevel\x12|\n" +
	"\x11strip_enum_prefix\x18\x03 \x01(\x0e2\x1e.pb.GoFeatures.StripEnumPrefixB0\x88\x01\x01\x98\x01\x06\x98\x01\a\x98\x01\x01\xa2\x01\x1b\x12\x16STRIP_ENUM_PREFIX_KEEP\x18\x84\a\xb2\x01\x03\b\xe9\aR\x0fstripEnumPrefix\"S\n" +
	"\bAPILevel\x12\x19\n" +
	"\x15API_LEVEL_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bAPI_OPEN\x10\x01\x12\x0e\n" +
	"\n" +
	"API_HYBRID\x10\x02\x12\x0e\n" +
	"\n" +
	"API_OPAQUE\x10\x03\"\x92\x01\n" +
	"\x0fStripEnumPrefix\x12!\n" +
	"\x1dSTRIP_ENUM_PREFIX_UNSPECIFIED\x10\x00\x12\x1a\n" +
	"\x16STRIP_ENUM_PREFIX_KEEP\x10\x01\x12#\n" +
	"\x1fSTRIP_ENUM_PREFIX_GENERATE_BOTH\x10\x02\x12\x1b\n" +
	"\x17STRIP_ENUM_PREFIX_STRIP\x10\x03:<\n" +
	"\x02go\x12\x1b.google.protobuf.FeatureSet\x18\xea\a \x01(\v2\x0e.pb.GoFeaturesR\x02goB/Z-google.golang.org/protobuf/types/gofeaturespb"

var (
	file_google_protobuf_go_features_proto_rawDescOnce sync.Once
	file_google_protobuf_go_features_proto_rawDescData []byte
)

func file_google_protobuf_go_features_proto_rawDescGZIP() []byte {
	file_google_protobuf_go_features_proto_rawDescOnce.Do(func() {
		file_google_protobuf_go_features_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_google_protobuf_go_features_proto_rawDesc), len(file_google_protobuf_go_features_proto_rawDesc)))
	})
	return file_google_protobuf_go_features_proto_rawDescData
}

var file_google_protobuf_go_features_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_google_protobuf_go_features_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_google_protobuf_go_features_proto_goTypes = []any{
	(GoFeatures_APILevel)(0),        // 0: pb.GoFeatures.APILevel
	(GoFeatures_StripEnumPrefix)(0), // 1: pb.GoFeatures.StripEnumPrefix
	(*GoFeatures)(nil),              // 2: pb.GoFeatures
	(*descriptorpb.FeatureSet)(nil), // 3: google.protobuf.FeatureSet
}
var file_google_protobuf_go_features_proto_depIdxs = []int32{
	0, // 0: pb.GoFeatures.api_level:type_name -> pb.GoFeatures.APILevel
	1, // 1: pb.GoFeatures.strip_enum_prefix:type_name -> pb.GoFeatures.StripEnumPrefix
	3, // 2: pb.go:extendee -> google.protobuf.FeatureSet
	2, // 3: pb.go:type_name -> pb.GoFeatures
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	3, // [3:4] is the sub-list for extension type_name
	2, // [2:3] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_google_protobuf_go_features_proto_init() }
func file_google_protobuf_go_features_proto_init() {
	if File_google_protobuf_go_features_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_google_protobuf_go_features_proto_rawDesc), len(file_google_protobuf_go_features_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 1,
			NumServices:   0,
		},
		GoTypes:           file_google_protobuf_go_features_proto_goTypes,
		DependencyIndexes: file_google_protobuf_go_features_proto_depIdxs,
		EnumInfos:         file_google_protobuf_go_features_proto_enumTypes,
		MessageInfos:      file_google_protobuf_go_features_proto_msgTypes,
		ExtensionInfos:    file_google_protobuf_go_features_proto_extTypes,
	}.Build()
	File_google_protobuf_go_features_proto = out.File
	file_google_protobuf_go_features_proto_goTypes = nil
	file_google_protobuf_go_features_proto_depIdxs = nil
}
