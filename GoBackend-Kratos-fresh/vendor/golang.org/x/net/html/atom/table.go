// Code generated by go generate gen.go; DO NOT EDIT.

//go:generate go run gen.go

package atom

const (
	A                         Atom = 0x1
	Abbr                      Atom = 0x4
	Accept                    Atom = 0x1a06
	AcceptCharset             Atom = 0x1a0e
	Accesskey                 Atom = 0x2c09
	Acronym                   Atom = 0xaa07
	Action                    Atom = 0x26506
	Address                   Atom = 0x6f107
	Align                     Atom = 0xb105
	Allowfullscreen           Atom = 0x3280f
	Allowpaymentrequest       Atom = 0xc113
	Allowusermedia            Atom = 0xdd0e
	Alt                       Atom = 0xf303
	Annotation                Atom = 0x1c90a
	AnnotationXml             Atom = 0x1c90e
	Applet                    Atom = 0x30806
	Area                      Atom = 0x35004
	Article                   Atom = 0x3f607
	As                        Atom = 0x3c02
	Aside                     Atom = 0x10705
	Async                     Atom = 0xff05
	Audio                     Atom = 0x11505
	Autocomplete              Atom = 0x26b0c
	Autofocus                 Atom = 0x12109
	Autoplay                  Atom = 0x13c08
	B                         Atom = 0x101
	Base                      Atom = 0x3b04
	Basefont                  Atom = 0x3b08
	Bdi                       Atom = 0xba03
	Bdo                       Atom = 0x14b03
	Bgsound                   Atom = 0x15e07
	Big                       Atom = 0x17003
	Blink                     Atom = 0x17305
	Blockquote                Atom = 0x1870a
	Body                      Atom = 0x2804
	Br                        Atom = 0x202
	Button                    Atom = 0x19106
	Canvas                    Atom = 0x10306
	Caption                   Atom = 0x22407
	Center                    Atom = 0x21306
	Challenge                 Atom = 0x28e09
	Charset                   Atom = 0x2107
	Checked                   Atom = 0x5b507
	Cite                      Atom = 0x19c04
	Class                     Atom = 0x55805
	Code                      Atom = 0x5ee04
	Col                       Atom = 0x1ab03
	Colgroup                  Atom = 0x1ab08
	Color                     Atom = 0x1bf05
	Cols                      Atom = 0x1c404
	Colspan                   Atom = 0x1c407
	Command                   Atom = 0x1d707
	Content                   Atom = 0x57b07
	Contenteditable           Atom = 0x57b0f
	Contextmenu               Atom = 0x37a0b
	Controls                  Atom = 0x1de08
	Coords                    Atom = 0x1f006
	Crossorigin               Atom = 0x1fa0b
	Data                      Atom = 0x49904
	Datalist                  Atom = 0x49908
	Datetime                  Atom = 0x2ab08
	Dd                        Atom = 0x2bf02
	Default                   Atom = 0x10a07
	Defer                     Atom = 0x5f005
	Del                       Atom = 0x44c03
	Desc                      Atom = 0x55504
	Details                   Atom = 0x7207
	Dfn                       Atom = 0x8703
	Dialog                    Atom = 0xbb06
	Dir                       Atom = 0x9303
	Dirname                   Atom = 0x9307
	Disabled                  Atom = 0x16408
	Div                       Atom = 0x16b03
	Dl                        Atom = 0x5d602
	Download                  Atom = 0x45d08
	Draggable                 Atom = 0x17a09
	Dropzone                  Atom = 0x3ff08
	Dt                        Atom = 0x64002
	Em                        Atom = 0x6e02
	Embed                     Atom = 0x6e05
	Enctype                   Atom = 0x28007
	Face                      Atom = 0x21104
	Fieldset                  Atom = 0x21908
	Figcaption                Atom = 0x2210a
	Figure                    Atom = 0x23b06
	Font                      Atom = 0x3f04
	Footer                    Atom = 0xf606
	For                       Atom = 0x24703
	ForeignObject             Atom = 0x2470d
	Foreignobject             Atom = 0x2540d
	Form                      Atom = 0x26104
	Formaction                Atom = 0x2610a
	Formenctype               Atom = 0x27c0b
	Formmethod                Atom = 0x2970a
	Formnovalidate            Atom = 0x2a10e
	Formtarget                Atom = 0x2b30a
	Frame                     Atom = 0x8b05
	Frameset                  Atom = 0x8b08
	H1                        Atom = 0x15c02
	H2                        Atom = 0x56102
	H3                        Atom = 0x2cd02
	H4                        Atom = 0x2fc02
	H5                        Atom = 0x33f02
	H6                        Atom = 0x34902
	Head                      Atom = 0x32004
	Header                    Atom = 0x32006
	Headers                   Atom = 0x32007
	Height                    Atom = 0x5206
	Hgroup                    Atom = 0x64206
	Hidden                    Atom = 0x2bd06
	High                      Atom = 0x2ca04
	Hr                        Atom = 0x15702
	Href                      Atom = 0x2cf04
	Hreflang                  Atom = 0x2cf08
	Html                      Atom = 0x5604
	HttpEquiv                 Atom = 0x2d70a
	I                         Atom = 0x601
	Icon                      Atom = 0x57a04
	Id                        Atom = 0x10902
	Iframe                    Atom = 0x2eb06
	Image                     Atom = 0x2f105
	Img                       Atom = 0x2f603
	Input                     Atom = 0x44505
	Inputmode                 Atom = 0x44509
	Ins                       Atom = 0x20303
	Integrity                 Atom = 0x23209
	Is                        Atom = 0x16502
	Isindex                   Atom = 0x2fe07
	Ismap                     Atom = 0x30505
	Itemid                    Atom = 0x38506
	Itemprop                  Atom = 0x19d08
	Itemref                   Atom = 0x3c707
	Itemscope                 Atom = 0x66f09
	Itemtype                  Atom = 0x30e08
	Kbd                       Atom = 0xb903
	Keygen                    Atom = 0x3206
	Keytype                   Atom = 0xd607
	Kind                      Atom = 0x17704
	Label                     Atom = 0x5905
	Lang                      Atom = 0x2d304
	Legend                    Atom = 0x18106
	Li                        Atom = 0xb202
	Link                      Atom = 0x17404
	List                      Atom = 0x49d04
	Listing                   Atom = 0x49d07
	Loop                      Atom = 0x5d04
	Low                       Atom = 0xc303
	Main                      Atom = 0x1004
	Malignmark                Atom = 0xb00a
	Manifest                  Atom = 0x6d508
	Map                       Atom = 0x30703
	Mark                      Atom = 0xb604
	Marquee                   Atom = 0x31607
	Math                      Atom = 0x31d04
	Max                       Atom = 0x33703
	Maxlength                 Atom = 0x33709
	Media                     Atom = 0xe605
	Mediagroup                Atom = 0xe60a
	Menu                      Atom = 0x38104
	Menuitem                  Atom = 0x38108
	Meta                      Atom = 0x4ac04
	Meter                     Atom = 0x9805
	Method                    Atom = 0x29b06
	Mglyph                    Atom = 0x2f706
	Mi                        Atom = 0x34102
	Min                       Atom = 0x34103
	Minlength                 Atom = 0x34109
	Mn                        Atom = 0x2a402
	Mo                        Atom = 0xa402
	Ms                        Atom = 0x67202
	Mtext                     Atom = 0x34b05
	Multiple                  Atom = 0x35908
	Muted                     Atom = 0x36105
	Name                      Atom = 0x9604
	Nav                       Atom = 0x1303
	Nobr                      Atom = 0x3704
	Noembed                   Atom = 0x6c07
	Noframes                  Atom = 0x8908
	Nomodule                  Atom = 0xa208
	Nonce                     Atom = 0x1a605
	Noscript                  Atom = 0x2c208
	Novalidate                Atom = 0x2a50a
	Object                    Atom = 0x25b06
	Ol                        Atom = 0x13702
	Onabort                   Atom = 0x19507
	Onafterprint              Atom = 0x2290c
	Onautocomplete            Atom = 0x2690e
	Onautocompleteerror       Atom = 0x26913
	Onauxclick                Atom = 0x6140a
	Onbeforeprint             Atom = 0x69c0d
	Onbeforeunload            Atom = 0x6e50e
	Onblur                    Atom = 0x1ea06
	Oncancel                  Atom = 0x11908
	Oncanplay                 Atom = 0x14d09
	Oncanplaythrough          Atom = 0x14d10
	Onchange                  Atom = 0x41508
	Onclick                   Atom = 0x2e407
	Onclose                   Atom = 0x36607
	Oncontextmenu             Atom = 0x3780d
	Oncopy                    Atom = 0x38b06
	Oncuechange               Atom = 0x3910b
	Oncut                     Atom = 0x39c05
	Ondblclick                Atom = 0x3a10a
	Ondrag                    Atom = 0x3ab06
	Ondragend                 Atom = 0x3ab09
	Ondragenter               Atom = 0x3b40b
	Ondragexit                Atom = 0x3bf0a
	Ondragleave               Atom = 0x3d90b
	Ondragover                Atom = 0x3e40a
	Ondragstart               Atom = 0x3ee0b
	Ondrop                    Atom = 0x3fd06
	Ondurationchange          Atom = 0x40d10
	Onemptied                 Atom = 0x40409
	Onended                   Atom = 0x41d07
	Onerror                   Atom = 0x42407
	Onfocus                   Atom = 0x42b07
	Onhashchange              Atom = 0x4370c
	Oninput                   Atom = 0x44307
	Oninvalid                 Atom = 0x44f09
	Onkeydown                 Atom = 0x45809
	Onkeypress                Atom = 0x4650a
	Onkeyup                   Atom = 0x47407
	Onlanguagechange          Atom = 0x48110
	Onload                    Atom = 0x49106
	Onloadeddata              Atom = 0x4910c
	Onloadedmetadata          Atom = 0x4a410
	Onloadend                 Atom = 0x4ba09
	Onloadstart               Atom = 0x4c30b
	Onmessage                 Atom = 0x4ce09
	Onmessageerror            Atom = 0x4ce0e
	Onmousedown               Atom = 0x4dc0b
	Onmouseenter              Atom = 0x4e70c
	Onmouseleave              Atom = 0x4f30c
	Onmousemove               Atom = 0x4ff0b
	Onmouseout                Atom = 0x50a0a
	Onmouseover               Atom = 0x5170b
	Onmouseup                 Atom = 0x52209
	Onmousewheel              Atom = 0x5300c
	Onoffline                 Atom = 0x53c09
	Ononline                  Atom = 0x54508
	Onpagehide                Atom = 0x54d0a
	Onpageshow                Atom = 0x5630a
	Onpaste                   Atom = 0x56f07
	Onpause                   Atom = 0x58a07
	Onplay                    Atom = 0x59406
	Onplaying                 Atom = 0x59409
	Onpopstate                Atom = 0x59d0a
	Onprogress                Atom = 0x5a70a
	Onratechange              Atom = 0x5bc0c
	Onrejectionhandled        Atom = 0x5c812
	Onreset                   Atom = 0x5da07
	Onresize                  Atom = 0x5e108
	Onscroll                  Atom = 0x5f508
	Onsecuritypolicyviolation Atom = 0x5fd19
	Onseeked                  Atom = 0x61e08
	Onseeking                 Atom = 0x62609
	Onselect                  Atom = 0x62f08
	Onshow                    Atom = 0x63906
	Onsort                    Atom = 0x64d06
	Onstalled                 Atom = 0x65709
	Onstorage                 Atom = 0x66009
	Onsubmit                  Atom = 0x66908
	Onsuspend                 Atom = 0x67909
	Ontimeupdate              Atom = 0x400c
	Ontoggle                  Atom = 0x68208
	Onunhandledrejection      Atom = 0x68a14
	Onunload                  Atom = 0x6a908
	Onvolumechange            Atom = 0x6b10e
	Onwaiting                 Atom = 0x6bf09
	Onwheel                   Atom = 0x6c807
	Open                      Atom = 0x1a304
	Optgroup                  Atom = 0x5f08
	Optimum                   Atom = 0x6cf07
	Option                    Atom = 0x6e106
	Output                    Atom = 0x51106
	P                         Atom = 0xc01
	Param                     Atom = 0xc05
	Pattern                   Atom = 0x6607
	Picture                   Atom = 0x7b07
	Ping                      Atom = 0xef04
	Placeholder               Atom = 0x1310b
	Plaintext                 Atom = 0x1b209
	Playsinline               Atom = 0x1400b
	Poster                    Atom = 0x64706
	Pre                       Atom = 0x46a03
	Preload                   Atom = 0x47a07
	Progress                  Atom = 0x5a908
	Prompt                    Atom = 0x52a06
	Public                    Atom = 0x57606
	Q                         Atom = 0xcf01
	Radiogroup                Atom = 0x30a
	Rb                        Atom = 0x3a02
	Readonly                  Atom = 0x35108
	Referrerpolicy            Atom = 0x3cb0e
	Rel                       Atom = 0x47b03
	Required                  Atom = 0x23f08
	Reversed                  Atom = 0x8008
	Rows                      Atom = 0x9c04
	Rowspan                   Atom = 0x9c07
	Rp                        Atom = 0x22f02
	Rt                        Atom = 0x19a02
	Rtc                       Atom = 0x19a03
	Ruby                      Atom = 0xfb04
	S                         Atom = 0x2501
	Samp                      Atom = 0x7804
	Sandbox                   Atom = 0x12907
	Scope                     Atom = 0x67305
	Scoped                    Atom = 0x67306
	Script                    Atom = 0x2c406
	Seamless                  Atom = 0x36b08
	Search                    Atom = 0x55c06
	Section                   Atom = 0x1e507
	Select                    Atom = 0x63106
	Selected                  Atom = 0x63108
	Shape                     Atom = 0x1f505
	Size                      Atom = 0x5e504
	Sizes                     Atom = 0x5e505
	Slot                      Atom = 0x20504
	Small                     Atom = 0x32605
	Sortable                  Atom = 0x64f08
	Sorted                    Atom = 0x37206
	Source                    Atom = 0x43106
	Spacer                    Atom = 0x46e06
	Span                      Atom = 0x9f04
	Spellcheck                Atom = 0x5b00a
	Src                       Atom = 0x5e903
	Srcdoc                    Atom = 0x5e906
	Srclang                   Atom = 0x6f707
	Srcset                    Atom = 0x6fe06
	Start                     Atom = 0x3f405
	Step                      Atom = 0x57304
	Strike                    Atom = 0xd206
	Strong                    Atom = 0x6db06
	Style                     Atom = 0x70405
	Sub                       Atom = 0x66b03
	Summary                   Atom = 0x70907
	Sup                       Atom = 0x71003
	Svg                       Atom = 0x71303
	System                    Atom = 0x71606
	Tabindex                  Atom = 0x4b208
	Table                     Atom = 0x58505
	Target                    Atom = 0x2b706
	Tbody                     Atom = 0x2705
	Td                        Atom = 0x9202
	Template                  Atom = 0x71908
	Textarea                  Atom = 0x34c08
	Tfoot                     Atom = 0xf505
	Th                        Atom = 0x15602
	Thead                     Atom = 0x31f05
	Time                      Atom = 0x4204
	Title                     Atom = 0x11005
	Tr                        Atom = 0xcc02
	Track                     Atom = 0x1ba05
	Translate                 Atom = 0x20809
	Tt                        Atom = 0x6802
	Type                      Atom = 0xd904
	Typemustmatch             Atom = 0x2830d
	U                         Atom = 0xb01
	Ul                        Atom = 0xa702
	Updateviacache            Atom = 0x460e
	Usemap                    Atom = 0x58e06
	Value                     Atom = 0x1505
	Var                       Atom = 0x16d03
	Video                     Atom = 0x2e005
	Wbr                       Atom = 0x56c03
	Width                     Atom = 0x63e05
	Workertype                Atom = 0x7210a
	Wrap                      Atom = 0x72b04
	Xmp                       Atom = 0x12f03
)

const hash0 = 0x84f70e16

const maxAtomLen = 25

var table = [1 << 9]Atom{
	0x1:   0x3ff08, // dropzone
	0x2:   0x3b08,  // basefont
	0x3:   0x23209, // integrity
	0x4:   0x43106, // source
	0x5:   0x2c09,  // accesskey
	0x6:   0x1a06,  // accept
	0x7:   0x6c807, // onwheel
	0xb:   0x47407, // onkeyup
	0xc:   0x32007, // headers
	0xd:   0x67306, // scoped
	0xe:   0x67909, // onsuspend
	0xf:   0x8908,  // noframes
	0x10:  0x1fa0b, // crossorigin
	0x11:  0x2e407, // onclick
	0x12:  0x3f405, // start
	0x13:  0x37a0b, // contextmenu
	0x14:  0x5e903, // src
	0x15:  0x1c404, // cols
	0x16:  0xbb06,  // dialog
	0x17:  0x47a07, // preload
	0x18:  0x3c707, // itemref
	0x1b:  0x2f105, // image
	0x1d:  0x4ba09, // onloadend
	0x1e:  0x45d08, // download
	0x1f:  0x46a03, // pre
	0x23:  0x2970a, // formmethod
	0x24:  0x71303, // svg
	0x25:  0xcf01,  // q
	0x26:  0x64002, // dt
	0x27:  0x1de08, // controls
	0x2a:  0x2804,  // body
	0x2b:  0xd206,  // strike
	0x2c:  0x3910b, // oncuechange
	0x2d:  0x4c30b, // onloadstart
	0x2e:  0x2fe07, // isindex
	0x2f:  0xb202,  // li
	0x30:  0x1400b, // playsinline
	0x31:  0x34102, // mi
	0x32:  0x30806, // applet
	0x33:  0x4ce09, // onmessage
	0x35:  0x13702, // ol
	0x36:  0x1a304, // open
	0x39:  0x14d09, // oncanplay
	0x3a:  0x6bf09, // onwaiting
	0x3b:  0x11908, // oncancel
	0x3c:  0x6a908, // onunload
	0x3e:  0x53c09, // onoffline
	0x3f:  0x1a0e,  // accept-charset
	0x40:  0x32004, // head
	0x42:  0x3ab09, // ondragend
	0x43:  0x1310b, // placeholder
	0x44:  0x2b30a, // formtarget
	0x45:  0x2540d, // foreignobject
	0x47:  0x400c,  // ontimeupdate
	0x48:  0xdd0e,  // allowusermedia
	0x4a:  0x69c0d, // onbeforeprint
	0x4b:  0x5604,  // html
	0x4c:  0x9f04,  // span
	0x4d:  0x64206, // hgroup
	0x4e:  0x16408, // disabled
	0x4f:  0x4204,  // time
	0x51:  0x42b07, // onfocus
	0x53:  0xb00a,  // malignmark
	0x55:  0x4650a, // onkeypress
	0x56:  0x55805, // class
	0x57:  0x1ab08, // colgroup
	0x58:  0x33709, // maxlength
	0x59:  0x5a908, // progress
	0x5b:  0x70405, // style
	0x5c:  0x2a10e, // formnovalidate
	0x5e:  0x38b06, // oncopy
	0x60:  0x26104, // form
	0x61:  0xf606,  // footer
	0x64:  0x30a,   // radiogroup
	0x66:  0xfb04,  // ruby
	0x67:  0x4ff0b, // onmousemove
	0x68:  0x19d08, // itemprop
	0x69:  0x2d70a, // http-equiv
	0x6a:  0x15602, // th
	0x6c:  0x6e02,  // em
	0x6d:  0x38108, // menuitem
	0x6e:  0x63106, // select
	0x6f:  0x48110, // onlanguagechange
	0x70:  0x31f05, // thead
	0x71:  0x15c02, // h1
	0x72:  0x5e906, // srcdoc
	0x75:  0x9604,  // name
	0x76:  0x19106, // button
	0x77:  0x55504, // desc
	0x78:  0x17704, // kind
	0x79:  0x1bf05, // color
	0x7c:  0x58e06, // usemap
	0x7d:  0x30e08, // itemtype
	0x7f:  0x6d508, // manifest
	0x81:  0x5300c, // onmousewheel
	0x82:  0x4dc0b, // onmousedown
	0x84:  0xc05,   // param
	0x85:  0x2e005, // video
	0x86:  0x4910c, // onloadeddata
	0x87:  0x6f107, // address
	0x8c:  0xef04,  // ping
	0x8d:  0x24703, // for
	0x8f:  0x62f08, // onselect
	0x90:  0x30703, // map
	0x92:  0xc01,   // p
	0x93:  0x8008,  // reversed
	0x94:  0x54d0a, // onpagehide
	0x95:  0x3206,  // keygen
	0x96:  0x34109, // minlength
	0x97:  0x3e40a, // ondragover
	0x98:  0x42407, // onerror
	0x9a:  0x2107,  // charset
	0x9b:  0x29b06, // method
	0x9c:  0x101,   // b
	0x9d:  0x68208, // ontoggle
	0x9e:  0x2bd06, // hidden
	0xa0:  0x3f607, // article
	0xa2:  0x63906, // onshow
	0xa3:  0x64d06, // onsort
	0xa5:  0x57b0f, // contenteditable
	0xa6:  0x66908, // onsubmit
	0xa8:  0x44f09, // oninvalid
	0xaa:  0x202,   // br
	0xab:  0x10902, // id
	0xac:  0x5d04,  // loop
	0xad:  0x5630a, // onpageshow
	0xb0:  0x2cf04, // href
	0xb2:  0x2210a, // figcaption
	0xb3:  0x2690e, // onautocomplete
	0xb4:  0x49106, // onload
	0xb6:  0x9c04,  // rows
	0xb7:  0x1a605, // nonce
	0xb8:  0x68a14, // onunhandledrejection
	0xbb:  0x21306, // center
	0xbc:  0x59406, // onplay
	0xbd:  0x33f02, // h5
	0xbe:  0x49d07, // listing
	0xbf:  0x57606, // public
	0xc2:  0x23b06, // figure
	0xc3:  0x57a04, // icon
	0xc4:  0x1ab03, // col
	0xc5:  0x47b03, // rel
	0xc6:  0xe605,  // media
	0xc7:  0x12109, // autofocus
	0xc8:  0x19a02, // rt
	0xca:  0x2d304, // lang
	0xcc:  0x49908, // datalist
	0xce:  0x2eb06, // iframe
	0xcf:  0x36105, // muted
	0xd0:  0x6140a, // onauxclick
	0xd2:  0x3c02,  // as
	0xd6:  0x3fd06, // ondrop
	0xd7:  0x1c90a, // annotation
	0xd8:  0x21908, // fieldset
	0xdb:  0x2cf08, // hreflang
	0xdc:  0x4e70c, // onmouseenter
	0xdd:  0x2a402, // mn
	0xde:  0xe60a,  // mediagroup
	0xdf:  0x9805,  // meter
	0xe0:  0x56c03, // wbr
	0xe2:  0x63e05, // width
	0xe3:  0x2290c, // onafterprint
	0xe4:  0x30505, // ismap
	0xe5:  0x1505,  // value
	0xe7:  0x1303,  // nav
	0xe8:  0x54508, // ononline
	0xe9:  0xb604,  // mark
	0xea:  0xc303,  // low
	0xeb:  0x3ee0b, // ondragstart
	0xef:  0x12f03, // xmp
	0xf0:  0x22407, // caption
	0xf1:  0xd904,  // type
	0xf2:  0x70907, // summary
	0xf3:  0x6802,  // tt
	0xf4:  0x20809, // translate
	0xf5:  0x1870a, // blockquote
	0xf8:  0x15702, // hr
	0xfa:  0x2705,  // tbody
	0xfc:  0x7b07,  // picture
	0xfd:  0x5206,  // height
	0xfe:  0x19c04, // cite
	0xff:  0x2501,  // s
	0x101: 0xff05,  // async
	0x102: 0x56f07, // onpaste
	0x103: 0x19507, // onabort
	0x104: 0x2b706, // target
	0x105: 0x14b03, // bdo
	0x106: 0x1f006, // coords
	0x107: 0x5e108, // onresize
	0x108: 0x71908, // template
	0x10a: 0x3a02,  // rb
	0x10b: 0x2a50a, // novalidate
	0x10c: 0x460e,  // updateviacache
	0x10d: 0x71003, // sup
	0x10e: 0x6c07,  // noembed
	0x10f: 0x16b03, // div
	0x110: 0x6f707, // srclang
	0x111: 0x17a09, // draggable
	0x112: 0x67305, // scope
	0x113: 0x5905,  // label
	0x114: 0x22f02, // rp
	0x115: 0x23f08, // required
	0x116: 0x3780d, // oncontextmenu
	0x117: 0x5e504, // size
	0x118: 0x5b00a, // spellcheck
	0x119: 0x3f04,  // font
	0x11a: 0x9c07,  // rowspan
	0x11b: 0x10a07, // default
	0x11d: 0x44307, // oninput
	0x11e: 0x38506, // itemid
	0x11f: 0x5ee04, // code
	0x120: 0xaa07,  // acronym
	0x121: 0x3b04,  // base
	0x125: 0x2470d, // foreignObject
	0x126: 0x2ca04, // high
	0x127: 0x3cb0e, // referrerpolicy
	0x128: 0x33703, // max
	0x129: 0x59d0a, // onpopstate
	0x12a: 0x2fc02, // h4
	0x12b: 0x4ac04, // meta
	0x12c: 0x17305, // blink
	0x12e: 0x5f508, // onscroll
	0x12f: 0x59409, // onplaying
	0x130: 0xc113,  // allowpaymentrequest
	0x131: 0x19a03, // rtc
	0x132: 0x72b04, // wrap
	0x134: 0x8b08,  // frameset
	0x135: 0x32605, // small
	0x137: 0x32006, // header
	0x138: 0x40409, // onemptied
	0x139: 0x34902, // h6
	0x13a: 0x35908, // multiple
	0x13c: 0x52a06, // prompt
	0x13f: 0x28e09, // challenge
	0x141: 0x4370c, // onhashchange
	0x142: 0x57b07, // content
	0x143: 0x1c90e, // annotation-xml
	0x144: 0x36607, // onclose
	0x145: 0x14d10, // oncanplaythrough
	0x148: 0x5170b, // onmouseover
	0x149: 0x64f08, // sortable
	0x14a: 0xa402,  // mo
	0x14b: 0x2cd02, // h3
	0x14c: 0x2c406, // script
	0x14d: 0x41d07, // onended
	0x14f: 0x64706, // poster
	0x150: 0x7210a, // workertype
	0x153: 0x1f505, // shape
	0x154: 0x4,     // abbr
	0x155: 0x1,     // a
	0x156: 0x2bf02, // dd
	0x157: 0x71606, // system
	0x158: 0x4ce0e, // onmessageerror
	0x159: 0x36b08, // seamless
	0x15a: 0x2610a, // formaction
	0x15b: 0x6e106, // option
	0x15c: 0x31d04, // math
	0x15d: 0x62609, // onseeking
	0x15e: 0x39c05, // oncut
	0x15f: 0x44c03, // del
	0x160: 0x11005, // title
	0x161: 0x11505, // audio
	0x162: 0x63108, // selected
	0x165: 0x3b40b, // ondragenter
	0x166: 0x46e06, // spacer
	0x167: 0x4a410, // onloadedmetadata
	0x168: 0x44505, // input
	0x16a: 0x58505, // table
	0x16b: 0x41508, // onchange
	0x16e: 0x5f005, // defer
	0x171: 0x50a0a, // onmouseout
	0x172: 0x20504, // slot
	0x175: 0x3704,  // nobr
	0x177: 0x1d707, // command
	0x17a: 0x7207,  // details
	0x17b: 0x38104, // menu
	0x17c: 0xb903,  // kbd
	0x17d: 0x57304, // step
	0x17e: 0x20303, // ins
	0x17f: 0x13c08, // autoplay
	0x182: 0x34103, // min
	0x183: 0x17404, // link
	0x185: 0x40d10, // ondurationchange
	0x186: 0x9202,  // td
	0x187: 0x8b05,  // frame
	0x18a: 0x2ab08, // datetime
	0x18b: 0x44509, // inputmode
	0x18c: 0x35108, // readonly
	0x18d: 0x21104, // face
	0x18f: 0x5e505, // sizes
	0x191: 0x4b208, // tabindex
	0x192: 0x6db06, // strong
	0x193: 0xba03,  // bdi
	0x194: 0x6fe06, // srcset
	0x196: 0x67202, // ms
	0x197: 0x5b507, // checked
	0x198: 0xb105,  // align
	0x199: 0x1e507, // section
	0x19b: 0x6e05,  // embed
	0x19d: 0x15e07, // bgsound
	0x1a2: 0x49d04, // list
	0x1a3: 0x61e08, // onseeked
	0x1a4: 0x66009, // onstorage
	0x1a5: 0x2f603, // img
	0x1a6: 0xf505,  // tfoot
	0x1a9: 0x26913, // onautocompleteerror
	0x1aa: 0x5fd19, // onsecuritypolicyviolation
	0x1ad: 0x9303,  // dir
	0x1ae: 0x9307,  // dirname
	0x1b0: 0x5a70a, // onprogress
	0x1b2: 0x65709, // onstalled
	0x1b5: 0x66f09, // itemscope
	0x1b6: 0x49904, // data
	0x1b7: 0x3d90b, // ondragleave
	0x1b8: 0x56102, // h2
	0x1b9: 0x2f706, // mglyph
	0x1ba: 0x16502, // is
	0x1bb: 0x6e50e, // onbeforeunload
	0x1bc: 0x2830d, // typemustmatch
	0x1bd: 0x3ab06, // ondrag
	0x1be: 0x5da07, // onreset
	0x1c0: 0x51106, // output
	0x1c1: 0x12907, // sandbox
	0x1c2: 0x1b209, // plaintext
	0x1c4: 0x34c08, // textarea
	0x1c7: 0xd607,  // keytype
	0x1c8: 0x34b05, // mtext
	0x1c9: 0x6b10e, // onvolumechange
	0x1ca: 0x1ea06, // onblur
	0x1cb: 0x58a07, // onpause
	0x1cd: 0x5bc0c, // onratechange
	0x1ce: 0x10705, // aside
	0x1cf: 0x6cf07, // optimum
	0x1d1: 0x45809, // onkeydown
	0x1d2: 0x1c407, // colspan
	0x1d3: 0x1004,  // main
	0x1d4: 0x66b03, // sub
	0x1d5: 0x25b06, // object
	0x1d6: 0x55c06, // search
	0x1d7: 0x37206, // sorted
	0x1d8: 0x17003, // big
	0x1d9: 0xb01,   // u
	0x1db: 0x26b0c, // autocomplete
	0x1dc: 0xcc02,  // tr
	0x1dd: 0xf303,  // alt
	0x1df: 0x7804,  // samp
	0x1e0: 0x5c812, // onrejectionhandled
	0x1e1: 0x4f30c, // onmouseleave
	0x1e2: 0x28007, // enctype
	0x1e3: 0xa208,  // nomodule
	0x1e5: 0x3280f, // allowfullscreen
	0x1e6: 0x5f08,  // optgroup
	0x1e8: 0x27c0b, // formenctype
	0x1e9: 0x18106, // legend
	0x1ea: 0x10306, // canvas
	0x1eb: 0x6607,  // pattern
	0x1ec: 0x2c208, // noscript
	0x1ed: 0x601,   // i
	0x1ee: 0x5d602, // dl
	0x1ef: 0xa702,  // ul
	0x1f2: 0x52209, // onmouseup
	0x1f4: 0x1ba05, // track
	0x1f7: 0x3a10a, // ondblclick
	0x1f8: 0x3bf0a, // ondragexit
	0x1fa: 0x8703,  // dfn
	0x1fc: 0x26506, // action
	0x1fd: 0x35004, // area
	0x1fe: 0x31607, // marquee
	0x1ff: 0x16d03, // var
}

const atomText = "abbradiogrouparamainavalueaccept-charsetbodyaccesskeygenobrb" +
	"asefontimeupdateviacacheightmlabelooptgroupatternoembedetail" +
	"sampictureversedfnoframesetdirnameterowspanomoduleacronymali" +
	"gnmarkbdialogallowpaymentrequestrikeytypeallowusermediagroup" +
	"ingaltfooterubyasyncanvasidefaultitleaudioncancelautofocusan" +
	"dboxmplaceholderautoplaysinlinebdoncanplaythrough1bgsoundisa" +
	"bledivarbigblinkindraggablegendblockquotebuttonabortcitempro" +
	"penoncecolgrouplaintextrackcolorcolspannotation-xmlcommandco" +
	"ntrolsectionblurcoordshapecrossoriginslotranslatefacenterfie" +
	"ldsetfigcaptionafterprintegrityfigurequiredforeignObjectfore" +
	"ignobjectformactionautocompleteerrorformenctypemustmatchalle" +
	"ngeformmethodformnovalidatetimeformtargethiddenoscripthigh3h" +
	"reflanghttp-equivideonclickiframeimageimglyph4isindexismappl" +
	"etitemtypemarqueematheadersmallowfullscreenmaxlength5minleng" +
	"th6mtextareadonlymultiplemutedoncloseamlessortedoncontextmen" +
	"uitemidoncopyoncuechangeoncutondblclickondragendondragentero" +
	"ndragexitemreferrerpolicyondragleaveondragoverondragstarticl" +
	"eondropzonemptiedondurationchangeonendedonerroronfocusourceo" +
	"nhashchangeoninputmodeloninvalidonkeydownloadonkeypresspacer" +
	"onkeyupreloadonlanguagechangeonloadeddatalistingonloadedmeta" +
	"databindexonloadendonloadstartonmessageerroronmousedownonmou" +
	"seenteronmouseleaveonmousemoveonmouseoutputonmouseoveronmous" +
	"eupromptonmousewheelonofflineononlineonpagehidesclassearch2o" +
	"npageshowbronpastepublicontenteditableonpausemaponplayingonp" +
	"opstateonprogresspellcheckedonratechangeonrejectionhandledon" +
	"resetonresizesrcdocodeferonscrollonsecuritypolicyviolationau" +
	"xclickonseekedonseekingonselectedonshowidthgrouposteronsorta" +
	"bleonstalledonstorageonsubmitemscopedonsuspendontoggleonunha" +
	"ndledrejectionbeforeprintonunloadonvolumechangeonwaitingonwh" +
	"eeloptimumanifestrongoptionbeforeunloaddressrclangsrcsetstyl" +
	"esummarysupsvgsystemplateworkertypewrap"
