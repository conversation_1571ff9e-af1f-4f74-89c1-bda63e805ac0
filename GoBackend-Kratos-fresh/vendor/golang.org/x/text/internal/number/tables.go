// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

package number

import "golang.org/x/text/internal/stringset"

// CLDRVersion is the CLDR version from which the tables in this package are derived.
const CLDRVersion = "32"

var numSysData = []systemData{ // 59 elements
	0:  {id: 0x0, digitSize: 0x1, zero: [4]uint8{0x30, 0x0, 0x0, 0x0}},
	1:  {id: 0x1, digitSize: 0x4, zero: [4]uint8{0xf0, 0x9e, 0xa5, 0x90}},
	2:  {id: 0x2, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x9c, 0xb0}},
	3:  {id: 0x3, digitSize: 0x2, zero: [4]uint8{0xd9, 0xa0, 0x0, 0x0}},
	4:  {id: 0x4, digitSize: 0x2, zero: [4]uint8{0xdb, 0xb0, 0x0, 0x0}},
	5:  {id: 0x5, digitSize: 0x3, zero: [4]uint8{0xe1, 0xad, 0x90, 0x0}},
	6:  {id: 0x6, digitSize: 0x3, zero: [4]uint8{0xe0, 0xa7, 0xa6, 0x0}},
	7:  {id: 0x7, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0xb1, 0x90}},
	8:  {id: 0x8, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x81, 0xa6}},
	9:  {id: 0x9, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x84, 0xb6}},
	10: {id: 0xa, digitSize: 0x3, zero: [4]uint8{0xea, 0xa9, 0x90, 0x0}},
	11: {id: 0xb, digitSize: 0x3, zero: [4]uint8{0xe0, 0xa5, 0xa6, 0x0}},
	12: {id: 0xc, digitSize: 0x3, zero: [4]uint8{0xef, 0xbc, 0x90, 0x0}},
	13: {id: 0xd, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0xb5, 0x90}},
	14: {id: 0xe, digitSize: 0x3, zero: [4]uint8{0xe0, 0xab, 0xa6, 0x0}},
	15: {id: 0xf, digitSize: 0x3, zero: [4]uint8{0xe0, 0xa9, 0xa6, 0x0}},
	16: {id: 0x10, digitSize: 0x4, zero: [4]uint8{0xf0, 0x96, 0xad, 0x90}},
	17: {id: 0x11, digitSize: 0x3, zero: [4]uint8{0xea, 0xa7, 0x90, 0x0}},
	18: {id: 0x12, digitSize: 0x3, zero: [4]uint8{0xea, 0xa4, 0x80, 0x0}},
	19: {id: 0x13, digitSize: 0x3, zero: [4]uint8{0xe1, 0x9f, 0xa0, 0x0}},
	20: {id: 0x14, digitSize: 0x3, zero: [4]uint8{0xe0, 0xb3, 0xa6, 0x0}},
	21: {id: 0x15, digitSize: 0x3, zero: [4]uint8{0xe1, 0xaa, 0x80, 0x0}},
	22: {id: 0x16, digitSize: 0x3, zero: [4]uint8{0xe1, 0xaa, 0x90, 0x0}},
	23: {id: 0x17, digitSize: 0x3, zero: [4]uint8{0xe0, 0xbb, 0x90, 0x0}},
	24: {id: 0x18, digitSize: 0x3, zero: [4]uint8{0xe1, 0xb1, 0x80, 0x0}},
	25: {id: 0x19, digitSize: 0x3, zero: [4]uint8{0xe1, 0xa5, 0x86, 0x0}},
	26: {id: 0x1a, digitSize: 0x4, zero: [4]uint8{0xf0, 0x9d, 0x9f, 0x8e}},
	27: {id: 0x1b, digitSize: 0x4, zero: [4]uint8{0xf0, 0x9d, 0x9f, 0x98}},
	28: {id: 0x1c, digitSize: 0x4, zero: [4]uint8{0xf0, 0x9d, 0x9f, 0xb6}},
	29: {id: 0x1d, digitSize: 0x4, zero: [4]uint8{0xf0, 0x9d, 0x9f, 0xac}},
	30: {id: 0x1e, digitSize: 0x4, zero: [4]uint8{0xf0, 0x9d, 0x9f, 0xa2}},
	31: {id: 0x1f, digitSize: 0x3, zero: [4]uint8{0xe0, 0xb5, 0xa6, 0x0}},
	32: {id: 0x20, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x99, 0x90}},
	33: {id: 0x21, digitSize: 0x3, zero: [4]uint8{0xe1, 0xa0, 0x90, 0x0}},
	34: {id: 0x22, digitSize: 0x4, zero: [4]uint8{0xf0, 0x96, 0xa9, 0xa0}},
	35: {id: 0x23, digitSize: 0x3, zero: [4]uint8{0xea, 0xaf, 0xb0, 0x0}},
	36: {id: 0x24, digitSize: 0x3, zero: [4]uint8{0xe1, 0x81, 0x80, 0x0}},
	37: {id: 0x25, digitSize: 0x3, zero: [4]uint8{0xe1, 0x82, 0x90, 0x0}},
	38: {id: 0x26, digitSize: 0x3, zero: [4]uint8{0xea, 0xa7, 0xb0, 0x0}},
	39: {id: 0x27, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x91, 0x90}},
	40: {id: 0x28, digitSize: 0x2, zero: [4]uint8{0xdf, 0x80, 0x0, 0x0}},
	41: {id: 0x29, digitSize: 0x3, zero: [4]uint8{0xe1, 0xb1, 0x90, 0x0}},
	42: {id: 0x2a, digitSize: 0x3, zero: [4]uint8{0xe0, 0xad, 0xa6, 0x0}},
	43: {id: 0x2b, digitSize: 0x4, zero: [4]uint8{0xf0, 0x90, 0x92, 0xa0}},
	44: {id: 0x2c, digitSize: 0x3, zero: [4]uint8{0xea, 0xa3, 0x90, 0x0}},
	45: {id: 0x2d, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x87, 0x90}},
	46: {id: 0x2e, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x8b, 0xb0}},
	47: {id: 0x2f, digitSize: 0x3, zero: [4]uint8{0xe0, 0xb7, 0xa6, 0x0}},
	48: {id: 0x30, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x83, 0xb0}},
	49: {id: 0x31, digitSize: 0x3, zero: [4]uint8{0xe1, 0xae, 0xb0, 0x0}},
	50: {id: 0x32, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x9b, 0x80}},
	51: {id: 0x33, digitSize: 0x3, zero: [4]uint8{0xe1, 0xa7, 0x90, 0x0}},
	52: {id: 0x34, digitSize: 0x3, zero: [4]uint8{0xe0, 0xaf, 0xa6, 0x0}},
	53: {id: 0x35, digitSize: 0x3, zero: [4]uint8{0xe0, 0xb1, 0xa6, 0x0}},
	54: {id: 0x36, digitSize: 0x3, zero: [4]uint8{0xe0, 0xb9, 0x90, 0x0}},
	55: {id: 0x37, digitSize: 0x3, zero: [4]uint8{0xe0, 0xbc, 0xa0, 0x0}},
	56: {id: 0x38, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0x93, 0x90}},
	57: {id: 0x39, digitSize: 0x3, zero: [4]uint8{0xea, 0x98, 0xa0, 0x0}},
	58: {id: 0x3a, digitSize: 0x4, zero: [4]uint8{0xf0, 0x91, 0xa3, 0xa0}},
} // Size: 378 bytes

const (
	numAdlm     = 0x1
	numAhom     = 0x2
	numArab     = 0x3
	numArabext  = 0x4
	numArmn     = 0x3b
	numArmnlow  = 0x3c
	numBali     = 0x5
	numBeng     = 0x6
	numBhks     = 0x7
	numBrah     = 0x8
	numCakm     = 0x9
	numCham     = 0xa
	numCyrl     = 0x3d
	numDeva     = 0xb
	numEthi     = 0x3e
	numFullwide = 0xc
	numGeor     = 0x3f
	numGonm     = 0xd
	numGrek     = 0x40
	numGreklow  = 0x41
	numGujr     = 0xe
	numGuru     = 0xf
	numHanidays = 0x42
	numHanidec  = 0x43
	numHans     = 0x44
	numHansfin  = 0x45
	numHant     = 0x46
	numHantfin  = 0x47
	numHebr     = 0x48
	numHmng     = 0x10
	numJava     = 0x11
	numJpan     = 0x49
	numJpanfin  = 0x4a
	numKali     = 0x12
	numKhmr     = 0x13
	numKnda     = 0x14
	numLana     = 0x15
	numLanatham = 0x16
	numLaoo     = 0x17
	numLatn     = 0x0
	numLepc     = 0x18
	numLimb     = 0x19
	numMathbold = 0x1a
	numMathdbl  = 0x1b
	numMathmono = 0x1c
	numMathsanb = 0x1d
	numMathsans = 0x1e
	numMlym     = 0x1f
	numModi     = 0x20
	numMong     = 0x21
	numMroo     = 0x22
	numMtei     = 0x23
	numMymr     = 0x24
	numMymrshan = 0x25
	numMymrtlng = 0x26
	numNewa     = 0x27
	numNkoo     = 0x28
	numOlck     = 0x29
	numOrya     = 0x2a
	numOsma     = 0x2b
	numRoman    = 0x4b
	numRomanlow = 0x4c
	numSaur     = 0x2c
	numShrd     = 0x2d
	numSind     = 0x2e
	numSinh     = 0x2f
	numSora     = 0x30
	numSund     = 0x31
	numTakr     = 0x32
	numTalu     = 0x33
	numTaml     = 0x4d
	numTamldec  = 0x34
	numTelu     = 0x35
	numThai     = 0x36
	numTibt     = 0x37
	numTirh     = 0x38
	numVaii     = 0x39
	numWara     = 0x3a
	numNumberSystems
)

var systemMap = map[string]system{
	"adlm":     numAdlm,
	"ahom":     numAhom,
	"arab":     numArab,
	"arabext":  numArabext,
	"armn":     numArmn,
	"armnlow":  numArmnlow,
	"bali":     numBali,
	"beng":     numBeng,
	"bhks":     numBhks,
	"brah":     numBrah,
	"cakm":     numCakm,
	"cham":     numCham,
	"cyrl":     numCyrl,
	"deva":     numDeva,
	"ethi":     numEthi,
	"fullwide": numFullwide,
	"geor":     numGeor,
	"gonm":     numGonm,
	"grek":     numGrek,
	"greklow":  numGreklow,
	"gujr":     numGujr,
	"guru":     numGuru,
	"hanidays": numHanidays,
	"hanidec":  numHanidec,
	"hans":     numHans,
	"hansfin":  numHansfin,
	"hant":     numHant,
	"hantfin":  numHantfin,
	"hebr":     numHebr,
	"hmng":     numHmng,
	"java":     numJava,
	"jpan":     numJpan,
	"jpanfin":  numJpanfin,
	"kali":     numKali,
	"khmr":     numKhmr,
	"knda":     numKnda,
	"lana":     numLana,
	"lanatham": numLanatham,
	"laoo":     numLaoo,
	"latn":     numLatn,
	"lepc":     numLepc,
	"limb":     numLimb,
	"mathbold": numMathbold,
	"mathdbl":  numMathdbl,
	"mathmono": numMathmono,
	"mathsanb": numMathsanb,
	"mathsans": numMathsans,
	"mlym":     numMlym,
	"modi":     numModi,
	"mong":     numMong,
	"mroo":     numMroo,
	"mtei":     numMtei,
	"mymr":     numMymr,
	"mymrshan": numMymrshan,
	"mymrtlng": numMymrtlng,
	"newa":     numNewa,
	"nkoo":     numNkoo,
	"olck":     numOlck,
	"orya":     numOrya,
	"osma":     numOsma,
	"roman":    numRoman,
	"romanlow": numRomanlow,
	"saur":     numSaur,
	"shrd":     numShrd,
	"sind":     numSind,
	"sinh":     numSinh,
	"sora":     numSora,
	"sund":     numSund,
	"takr":     numTakr,
	"talu":     numTalu,
	"taml":     numTaml,
	"tamldec":  numTamldec,
	"telu":     numTelu,
	"thai":     numThai,
	"tibt":     numTibt,
	"tirh":     numTirh,
	"vaii":     numVaii,
	"wara":     numWara,
}

var symIndex = [][12]uint8{ // 81 elements
	0:  [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	1:  [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	2:  [12]uint8{0x0, 0x1, 0x2, 0xd, 0xe, 0xf, 0x6, 0x7, 0x8, 0x9, 0x10, 0xb},
	3:  [12]uint8{0x1, 0x0, 0x2, 0xd, 0xe, 0xf, 0x6, 0x7, 0x8, 0x9, 0x10, 0xb},
	4:  [12]uint8{0x0, 0x1, 0x2, 0x11, 0xe, 0xf, 0x6, 0x7, 0x8, 0x9, 0x10, 0xb},
	5:  [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x12, 0xb},
	6:  [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	7:  [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x13, 0xb},
	8:  [12]uint8{0x0, 0x1, 0x2, 0x3, 0xe, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	9:  [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0x0},
	10: [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x5, 0x6, 0x14, 0x8, 0x9, 0xa, 0xb},
	11: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x14, 0x8, 0x9, 0xa, 0xb},
	12: [12]uint8{0x0, 0x15, 0x2, 0x3, 0x4, 0x5, 0x6, 0x14, 0x8, 0x9, 0xa, 0xb},
	13: [12]uint8{0x0, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	14: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x16, 0xb},
	15: [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x5, 0x17, 0x7, 0x8, 0x9, 0xa, 0xb},
	16: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x17, 0x7, 0x8, 0x9, 0xa, 0x0},
	17: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x17, 0x7, 0x8, 0x9, 0xa, 0xb},
	18: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0x0},
	19: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x18, 0x7, 0x8, 0x9, 0xa, 0xb},
	20: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x19, 0x1a, 0xa, 0xb},
	21: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x1b, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	22: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x1b, 0x18, 0x7, 0x8, 0x9, 0xa, 0xb},
	23: [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x1b, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	24: [12]uint8{0x0, 0x1, 0x2, 0x3, 0xe, 0x1c, 0x6, 0x7, 0x8, 0x9, 0x1d, 0xb},
	25: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x1b, 0x6, 0x7, 0x8, 0x9, 0x1e, 0x0},
	26: [12]uint8{0x0, 0x15, 0x2, 0x3, 0x4, 0x1b, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	27: [12]uint8{0x0, 0x1, 0x2, 0x3, 0xe, 0xf, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	28: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x1f, 0xb},
	29: [12]uint8{0x0, 0x15, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	30: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x20, 0xb},
	31: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x21, 0x7, 0x8, 0x9, 0x22, 0xb},
	32: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x23, 0xb},
	33: [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x1b, 0x18, 0x14, 0x8, 0x9, 0x24, 0xb},
	34: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x1b, 0x18, 0x7, 0x8, 0x9, 0x24, 0xb},
	35: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x25, 0xb},
	36: [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x26, 0xb},
	37: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x27, 0xb},
	38: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x28, 0xb},
	39: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x29, 0xb},
	40: [12]uint8{0x1, 0x0, 0x2, 0x3, 0xe, 0x1c, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	41: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x2a, 0xb},
	42: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x2b, 0xb},
	43: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x1b, 0x2c, 0x14, 0x8, 0x9, 0x24, 0xb},
	44: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0x0},
	45: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x17, 0x7, 0x8, 0x9, 0xa, 0xb},
	46: [12]uint8{0x1, 0x0, 0x2, 0x3, 0x4, 0x1b, 0x17, 0x7, 0x8, 0x9, 0xa, 0xb},
	47: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x2d, 0x0},
	48: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x2e, 0xb},
	49: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x2f, 0xb},
	50: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x30, 0x7, 0x8, 0x9, 0xa, 0xb},
	51: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x31, 0xb},
	52: [12]uint8{0x1, 0xc, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x32, 0xb},
	53: [12]uint8{0x1, 0x15, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0xb},
	54: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x33, 0xb},
	55: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x34, 0xb},
	56: [12]uint8{0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x7, 0x3c, 0x9, 0xa, 0xb},
	57: [12]uint8{0x35, 0x36, 0x37, 0x38, 0x39, 0x3a, 0x3b, 0x7, 0x3c, 0x9, 0x3d, 0xb},
	58: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x3e, 0x3f, 0x3b, 0x7, 0x3c, 0x9, 0xa, 0xb},
	59: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x39, 0x3a, 0x3b, 0x7, 0x3c, 0x9, 0xa, 0xb},
	60: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x39, 0x40, 0x3b, 0x7, 0x3c, 0x9, 0xa, 0xb},
	61: [12]uint8{0x35, 0x36, 0x37, 0x41, 0x3e, 0x3f, 0x3b, 0x7, 0x3c, 0x9, 0xa, 0xb},
	62: [12]uint8{0x35, 0x36, 0x37, 0x38, 0x3e, 0x3f, 0x3b, 0x7, 0x3c, 0x9, 0xa, 0xb},
	63: [12]uint8{0x35, 0xc, 0x37, 0x38, 0x39, 0x42, 0x3b, 0x7, 0x3c, 0x9, 0xa, 0x0},
	64: [12]uint8{0x35, 0xc, 0x37, 0x38, 0x39, 0x42, 0x43, 0x7, 0x44, 0x9, 0x24, 0xb},
	65: [12]uint8{0x35, 0x36, 0x37, 0x38, 0x39, 0x5, 0x3b, 0x7, 0x3c, 0x9, 0x33, 0xb},
	66: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x45, 0x46, 0x43, 0x7, 0x3c, 0x9, 0xa, 0x35},
	67: [12]uint8{0x35, 0x36, 0x37, 0x11, 0xe, 0x1c, 0x43, 0x7, 0x3c, 0x9, 0x1d, 0xb},
	68: [12]uint8{0x35, 0x36, 0x37, 0x11, 0xe, 0x1c, 0x43, 0x7, 0x3c, 0x9, 0xa, 0x35},
	69: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x45, 0x5, 0x43, 0x7, 0x3c, 0x9, 0xa, 0x35},
	70: [12]uint8{0x1, 0xc, 0x37, 0x11, 0x45, 0x47, 0x43, 0x7, 0x3c, 0x9, 0xa, 0x0},
	71: [12]uint8{0x35, 0x1, 0x37, 0x11, 0x4, 0x5, 0x43, 0x7, 0x3c, 0x9, 0xa, 0x35},
	72: [12]uint8{0x1, 0xc, 0x37, 0x11, 0x45, 0x47, 0x43, 0x7, 0x3c, 0x9, 0x24, 0xb},
	73: [12]uint8{0x35, 0x36, 0x2, 0x3, 0x45, 0x46, 0x43, 0x7, 0x8, 0x9, 0xa, 0x35},
	74: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x4, 0x5, 0x43, 0x7, 0x3c, 0x9, 0x31, 0x35},
	75: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x4, 0x5, 0x43, 0x7, 0x3c, 0x9, 0x32, 0x35},
	76: [12]uint8{0x35, 0x36, 0x37, 0x11, 0x48, 0x46, 0x43, 0x7, 0x3c, 0x9, 0x33, 0x35},
	77: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xa, 0x49},
	78: [12]uint8{0x0, 0x1, 0x4a, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x28, 0xb},
	79: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0x4b, 0xb},
	80: [12]uint8{0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x4c, 0x4d, 0xb},
} // Size: 996 bytes

var symData = stringset.Set{
	Data: "" + // Size: 599 bytes
		".,;%+-E×‰∞NaN:\u00a0\u200e%\u200e\u200e+\u200e-ليس\u00a0رقمًا٪NDТерхьаш" +
		"\u00a0дац·’mnne×10^0/00INF−\u200e−ناعددepälukuՈչԹარ\u00a0არის\u00a0რიცხვ" +
		"იZMdMсан\u00a0емес¤¤¤сан\u00a0эмесບໍ່\u200bແມ່ນ\u200bໂຕ\u200bເລກNSဂဏန်" +
		"းမဟုတ်သောННне\u00a0числочыыһыла\u00a0буотах·10^epilohosan\u00a0dälTFЕs" +
		"on\u00a0emasҳақиқий\u00a0сон\u00a0эмас非數值非数值٫٬؛٪\u061c\u061c+\u061c-اس؉ل" +
		"يس\u00a0رقم\u200f+\u200f-\u200f−٪\u200f\u061c−×۱۰^؉\u200f\u200e+\u200e" +
		"\u200e-\u200e\u200e−\u200e+\u200e：၊ཨང་མེན་གྲངས་མེདཨང་མད",
	Index: []uint16{ // 79 elements
		// Entry 0 - 3F
		0x0000, 0x0001, 0x0002, 0x0003, 0x0004, 0x0005, 0x0006, 0x0007,
		0x0009, 0x000c, 0x000f, 0x0012, 0x0013, 0x0015, 0x001c, 0x0020,
		0x0024, 0x0036, 0x0038, 0x003a, 0x0050, 0x0052, 0x0055, 0x0058,
		0x0059, 0x005e, 0x0062, 0x0065, 0x0068, 0x006e, 0x0078, 0x0080,
		0x0086, 0x00ae, 0x00af, 0x00b2, 0x00c2, 0x00c8, 0x00d8, 0x0105,
		0x0107, 0x012e, 0x0132, 0x0142, 0x015e, 0x0163, 0x016a, 0x0173,
		0x0175, 0x0177, 0x0180, 0x01a0, 0x01a9, 0x01b2, 0x01b4, 0x01b6,
		0x01b8, 0x01bc, 0x01bf, 0x01c2, 0x01c6, 0x01c8, 0x01d6, 0x01da,
		// Entry 40 - 7F
		0x01de, 0x01e4, 0x01e9, 0x01ee, 0x01f5, 0x01fa, 0x0201, 0x0208,
		0x0211, 0x0215, 0x0218, 0x021b, 0x0230, 0x0248, 0x0257,
	},
} // Size: 797 bytes

// langToDefaults maps a compact language index to the default numbering system
// and default symbol set
var langToDefaults = [775]symOffset{
	// Entry 0 - 3F
	0x8000, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0000, 0x0000,
	0x0000, 0x0000, 0x8003, 0x0002, 0x0002, 0x0002, 0x0002, 0x0003,
	0x0002, 0x0002, 0x0002, 0x0002, 0x0002, 0x0002, 0x0002, 0x0002,
	0x0003, 0x0003, 0x0003, 0x0003, 0x0002, 0x0002, 0x0002, 0x0004,
	0x0002, 0x0004, 0x0002, 0x0002, 0x0002, 0x0003, 0x0002, 0x0000,
	0x8005, 0x0000, 0x0000, 0x0000, 0x8006, 0x0005, 0x0006, 0x0006,
	0x0006, 0x0006, 0x0006, 0x0001, 0x0001, 0x0001, 0x0001, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0001, 0x0001, 0x0000, 0x0000, 0x0000,
	// Entry 40 - 7F
	0x8009, 0x0000, 0x0000, 0x800a, 0x0000, 0x0000, 0x800c, 0x0001,
	0x0000, 0x0000, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006,
	0x0006, 0x0006, 0x0006, 0x0006, 0x800e, 0x0000, 0x0000, 0x0007,
	0x0007, 0x0000, 0x0000, 0x0000, 0x0000, 0x800f, 0x0008, 0x0008,
	0x8011, 0x0001, 0x0001, 0x0001, 0x803c, 0x0000, 0x0009, 0x0009,
	0x0009, 0x0000, 0x0000, 0x000a, 0x000b, 0x000a, 0x000c, 0x000a,
	0x000a, 0x000c, 0x000a, 0x000d, 0x000d, 0x000a, 0x000a, 0x0001,
	0x0001, 0x0000, 0x0001, 0x0001, 0x803f, 0x0000, 0x0000, 0x0000,
	// Entry 80 - BF
	0x000e, 0x000e, 0x000e, 0x000f, 0x000f, 0x000f, 0x0000, 0x0000,
	0x0006, 0x0000, 0x0000, 0x0000, 0x000a, 0x0010, 0x0000, 0x0006,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0011, 0x0000, 0x000a,
	0x0000, 0x0000, 0x0000, 0x0000, 0x000a, 0x0000, 0x0009, 0x0000,
	0x0000, 0x0012, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	// Entry C0 - FF
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0013, 0x0000,
	0x0000, 0x000f, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0001, 0x0000, 0x0000, 0x0015,
	0x0015, 0x0006, 0x0000, 0x0006, 0x0006, 0x0000, 0x0000, 0x0006,
	0x0006, 0x0001, 0x0000, 0x0000, 0x0006, 0x0006, 0x0006, 0x0006,
	// Entry 100 - 13F
	0x0000, 0x0000, 0x0006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0006,
	0x0000, 0x0006, 0x0000, 0x0000, 0x0006, 0x0006, 0x0016, 0x0016,
	0x0017, 0x0017, 0x0001, 0x0001, 0x8041, 0x0018, 0x0018, 0x0001,
	0x0001, 0x0001, 0x0001, 0x0001, 0x0019, 0x0019, 0x0000, 0x0000,
	0x0017, 0x0017, 0x0017, 0x8044, 0x0001, 0x0001, 0x0001, 0x0001,
	0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001,
	0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001,
	0x0001, 0x0001, 0x0006, 0x0006, 0x0001, 0x0001, 0x0001, 0x0001,
	// Entry 140 - 17F
	0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001,
	0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001,
	0x0001, 0x0001, 0x0006, 0x0006, 0x0006, 0x0006, 0x0000, 0x0000,
	0x8047, 0x0000, 0x0006, 0x0006, 0x001a, 0x001a, 0x001a, 0x001a,
	0x804a, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x804c, 0x001b, 0x0000,
	0x0000, 0x0006, 0x0006, 0x0006, 0x000a, 0x000a, 0x0001, 0x0001,
	0x001c, 0x001c, 0x0009, 0x0009, 0x804f, 0x0000, 0x0000, 0x0000,
	// Entry 180 - 1BF
	0x0000, 0x0000, 0x8052, 0x0006, 0x0006, 0x001d, 0x0006, 0x0006,
	0x0006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0006, 0x0006,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x001e, 0x001e, 0x001f,
	0x001f, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0001,
	0x0001, 0x000d, 0x000d, 0x0000, 0x0000, 0x0020, 0x0020, 0x0006,
	0x0006, 0x0021, 0x0021, 0x0000, 0x0000, 0x0006, 0x0006, 0x0000,
	0x0000, 0x8054, 0x0000, 0x0000, 0x0000, 0x0000, 0x8056, 0x001b,
	0x0000, 0x0000, 0x0001, 0x0001, 0x0022, 0x0022, 0x0000, 0x0000,
	// Entry 1C0 - 1FF
	0x0000, 0x0023, 0x0023, 0x0000, 0x0000, 0x0006, 0x0006, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006,
	0x0024, 0x0024, 0x8058, 0x0000, 0x0000, 0x0016, 0x0016, 0x0006,
	0x0006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0025, 0x0025, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x000d, 0x000d, 0x0000, 0x0000,
	0x0006, 0x0006, 0x0000, 0x0000, 0x0006, 0x0006, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x805a, 0x0000, 0x0000, 0x0006, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0006, 0x0006, 0x805b, 0x0026, 0x805d,
	// Entry 200 - 23F
	0x0000, 0x0000, 0x0000, 0x0000, 0x805e, 0x0015, 0x0015, 0x0000,
	0x0000, 0x0006, 0x0006, 0x0006, 0x8061, 0x0000, 0x0000, 0x8062,
	0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x0001,
	0x0001, 0x0015, 0x0015, 0x0006, 0x0006, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0027, 0x0027, 0x0027, 0x8065, 0x8067,
	0x001b, 0x0000, 0x0000, 0x0000, 0x0001, 0x0001, 0x0001, 0x0001,
	0x8069, 0x0028, 0x0006, 0x0001, 0x0006, 0x0001, 0x0001, 0x0001,
	// Entry 240 - 27F
	0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001, 0x0000,
	0x0006, 0x0000, 0x0000, 0x001a, 0x001a, 0x0006, 0x0006, 0x0006,
	0x0006, 0x0006, 0x0000, 0x0000, 0x0029, 0x0029, 0x0029, 0x0029,
	0x0029, 0x0029, 0x0029, 0x0006, 0x0006, 0x0000, 0x0000, 0x002a,
	0x002a, 0x0000, 0x0000, 0x0000, 0x0000, 0x806b, 0x0000, 0x0000,
	0x002b, 0x002b, 0x002b, 0x002b, 0x0006, 0x0006, 0x000d, 0x000d,
	0x0006, 0x0006, 0x0000, 0x0001, 0x0001, 0x0001, 0x0001, 0x0001,
	0x002c, 0x002c, 0x002d, 0x002d, 0x002e, 0x002e, 0x0000, 0x0000,
	// Entry 280 - 2BF
	0x0000, 0x002f, 0x002f, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0001, 0x0001, 0x0001, 0x0001, 0x0006,
	0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006, 0x0006,
	0x0006, 0x0006, 0x0000, 0x0000, 0x0000, 0x806d, 0x0022, 0x0022,
	0x0022, 0x0000, 0x0006, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0001, 0x0001, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0030, 0x0030, 0x0000, 0x0000, 0x8071, 0x0031, 0x0006,
	// Entry 2C0 - 2FF
	0x0006, 0x0006, 0x0000, 0x0001, 0x0001, 0x000d, 0x000d, 0x0001,
	0x0001, 0x0000, 0x0000, 0x0032, 0x0032, 0x8074, 0x8076, 0x001b,
	0x8077, 0x8079, 0x0028, 0x807b, 0x0034, 0x0033, 0x0033, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x0006, 0x0006, 0x0000,
	0x0000, 0x0000, 0x0000, 0x0000, 0x0035, 0x0035, 0x0006, 0x0006,
	0x0000, 0x0000, 0x0000, 0x0001, 0x0001, 0x0000, 0x0000, 0x0000,
	0x0000, 0x0000, 0x0036, 0x0037, 0x0037, 0x0036, 0x0036, 0x0001,
	0x0001, 0x807d, 0x0000, 0x0000, 0x0000, 0x0000, 0x0000, 0x8080,
	// Entry 300 - 33F
	0x0036, 0x0036, 0x0036, 0x0000, 0x0000, 0x0006, 0x0014,
} // Size: 1550 bytes

// langToAlt is a list of numbering system and symbol set pairs, sorted and
// marked by compact language index.
var langToAlt = []altSymData{ // 131 elements
	1:   {compactTag: 0x0, symIndex: 0x38, system: 0x3},
	2:   {compactTag: 0x0, symIndex: 0x42, system: 0x4},
	3:   {compactTag: 0xa, symIndex: 0x39, system: 0x3},
	4:   {compactTag: 0xa, symIndex: 0x2, system: 0x0},
	5:   {compactTag: 0x28, symIndex: 0x0, system: 0x6},
	6:   {compactTag: 0x2c, symIndex: 0x5, system: 0x0},
	7:   {compactTag: 0x2c, symIndex: 0x3a, system: 0x3},
	8:   {compactTag: 0x2c, symIndex: 0x42, system: 0x4},
	9:   {compactTag: 0x40, symIndex: 0x0, system: 0x6},
	10:  {compactTag: 0x43, symIndex: 0x0, system: 0x0},
	11:  {compactTag: 0x43, symIndex: 0x4f, system: 0x37},
	12:  {compactTag: 0x46, symIndex: 0x1, system: 0x0},
	13:  {compactTag: 0x46, symIndex: 0x38, system: 0x3},
	14:  {compactTag: 0x54, symIndex: 0x0, system: 0x9},
	15:  {compactTag: 0x5d, symIndex: 0x3a, system: 0x3},
	16:  {compactTag: 0x5d, symIndex: 0x8, system: 0x0},
	17:  {compactTag: 0x60, symIndex: 0x1, system: 0x0},
	18:  {compactTag: 0x60, symIndex: 0x38, system: 0x3},
	19:  {compactTag: 0x60, symIndex: 0x42, system: 0x4},
	20:  {compactTag: 0x60, symIndex: 0x0, system: 0x5},
	21:  {compactTag: 0x60, symIndex: 0x0, system: 0x6},
	22:  {compactTag: 0x60, symIndex: 0x0, system: 0x8},
	23:  {compactTag: 0x60, symIndex: 0x0, system: 0x9},
	24:  {compactTag: 0x60, symIndex: 0x0, system: 0xa},
	25:  {compactTag: 0x60, symIndex: 0x0, system: 0xb},
	26:  {compactTag: 0x60, symIndex: 0x0, system: 0xc},
	27:  {compactTag: 0x60, symIndex: 0x0, system: 0xd},
	28:  {compactTag: 0x60, symIndex: 0x0, system: 0xe},
	29:  {compactTag: 0x60, symIndex: 0x0, system: 0xf},
	30:  {compactTag: 0x60, symIndex: 0x0, system: 0x11},
	31:  {compactTag: 0x60, symIndex: 0x0, system: 0x12},
	32:  {compactTag: 0x60, symIndex: 0x0, system: 0x13},
	33:  {compactTag: 0x60, symIndex: 0x0, system: 0x14},
	34:  {compactTag: 0x60, symIndex: 0x0, system: 0x15},
	35:  {compactTag: 0x60, symIndex: 0x0, system: 0x16},
	36:  {compactTag: 0x60, symIndex: 0x0, system: 0x17},
	37:  {compactTag: 0x60, symIndex: 0x0, system: 0x18},
	38:  {compactTag: 0x60, symIndex: 0x0, system: 0x19},
	39:  {compactTag: 0x60, symIndex: 0x0, system: 0x1f},
	40:  {compactTag: 0x60, symIndex: 0x0, system: 0x21},
	41:  {compactTag: 0x60, symIndex: 0x0, system: 0x23},
	42:  {compactTag: 0x60, symIndex: 0x0, system: 0x24},
	43:  {compactTag: 0x60, symIndex: 0x0, system: 0x25},
	44:  {compactTag: 0x60, symIndex: 0x0, system: 0x28},
	45:  {compactTag: 0x60, symIndex: 0x0, system: 0x29},
	46:  {compactTag: 0x60, symIndex: 0x0, system: 0x2a},
	47:  {compactTag: 0x60, symIndex: 0x0, system: 0x2b},
	48:  {compactTag: 0x60, symIndex: 0x0, system: 0x2c},
	49:  {compactTag: 0x60, symIndex: 0x0, system: 0x2d},
	50:  {compactTag: 0x60, symIndex: 0x0, system: 0x30},
	51:  {compactTag: 0x60, symIndex: 0x0, system: 0x31},
	52:  {compactTag: 0x60, symIndex: 0x0, system: 0x32},
	53:  {compactTag: 0x60, symIndex: 0x0, system: 0x33},
	54:  {compactTag: 0x60, symIndex: 0x0, system: 0x34},
	55:  {compactTag: 0x60, symIndex: 0x0, system: 0x35},
	56:  {compactTag: 0x60, symIndex: 0x0, system: 0x36},
	57:  {compactTag: 0x60, symIndex: 0x0, system: 0x37},
	58:  {compactTag: 0x60, symIndex: 0x0, system: 0x39},
	59:  {compactTag: 0x60, symIndex: 0x0, system: 0x43},
	60:  {compactTag: 0x64, symIndex: 0x0, system: 0x0},
	61:  {compactTag: 0x64, symIndex: 0x38, system: 0x3},
	62:  {compactTag: 0x64, symIndex: 0x42, system: 0x4},
	63:  {compactTag: 0x7c, symIndex: 0x50, system: 0x37},
	64:  {compactTag: 0x7c, symIndex: 0x0, system: 0x0},
	65:  {compactTag: 0x114, symIndex: 0x43, system: 0x4},
	66:  {compactTag: 0x114, symIndex: 0x18, system: 0x0},
	67:  {compactTag: 0x114, symIndex: 0x3b, system: 0x3},
	68:  {compactTag: 0x123, symIndex: 0x1, system: 0x0},
	69:  {compactTag: 0x123, symIndex: 0x3c, system: 0x3},
	70:  {compactTag: 0x123, symIndex: 0x44, system: 0x4},
	71:  {compactTag: 0x158, symIndex: 0x0, system: 0x0},
	72:  {compactTag: 0x158, symIndex: 0x3b, system: 0x3},
	73:  {compactTag: 0x158, symIndex: 0x45, system: 0x4},
	74:  {compactTag: 0x160, symIndex: 0x0, system: 0x0},
	75:  {compactTag: 0x160, symIndex: 0x38, system: 0x3},
	76:  {compactTag: 0x16d, symIndex: 0x1b, system: 0x0},
	77:  {compactTag: 0x16d, symIndex: 0x0, system: 0x9},
	78:  {compactTag: 0x16d, symIndex: 0x0, system: 0xa},
	79:  {compactTag: 0x17c, symIndex: 0x0, system: 0x0},
	80:  {compactTag: 0x17c, symIndex: 0x3d, system: 0x3},
	81:  {compactTag: 0x17c, symIndex: 0x42, system: 0x4},
	82:  {compactTag: 0x182, symIndex: 0x6, system: 0x0},
	83:  {compactTag: 0x182, symIndex: 0x38, system: 0x3},
	84:  {compactTag: 0x1b1, symIndex: 0x0, system: 0x0},
	85:  {compactTag: 0x1b1, symIndex: 0x3e, system: 0x3},
	86:  {compactTag: 0x1b6, symIndex: 0x42, system: 0x4},
	87:  {compactTag: 0x1b6, symIndex: 0x1b, system: 0x0},
	88:  {compactTag: 0x1d2, symIndex: 0x42, system: 0x4},
	89:  {compactTag: 0x1d2, symIndex: 0x0, system: 0x0},
	90:  {compactTag: 0x1f3, symIndex: 0x0, system: 0xb},
	91:  {compactTag: 0x1fd, symIndex: 0x4e, system: 0x24},
	92:  {compactTag: 0x1fd, symIndex: 0x26, system: 0x0},
	93:  {compactTag: 0x1ff, symIndex: 0x42, system: 0x4},
	94:  {compactTag: 0x204, symIndex: 0x15, system: 0x0},
	95:  {compactTag: 0x204, symIndex: 0x3f, system: 0x3},
	96:  {compactTag: 0x204, symIndex: 0x46, system: 0x4},
	97:  {compactTag: 0x20c, symIndex: 0x0, system: 0xb},
	98:  {compactTag: 0x20f, symIndex: 0x6, system: 0x0},
	99:  {compactTag: 0x20f, symIndex: 0x38, system: 0x3},
	100: {compactTag: 0x20f, symIndex: 0x42, system: 0x4},
	101: {compactTag: 0x22e, symIndex: 0x0, system: 0x0},
	102: {compactTag: 0x22e, symIndex: 0x47, system: 0x4},
	103: {compactTag: 0x22f, symIndex: 0x42, system: 0x4},
	104: {compactTag: 0x22f, symIndex: 0x1b, system: 0x0},
	105: {compactTag: 0x238, symIndex: 0x42, system: 0x4},
	106: {compactTag: 0x238, symIndex: 0x28, system: 0x0},
	107: {compactTag: 0x265, symIndex: 0x38, system: 0x3},
	108: {compactTag: 0x265, symIndex: 0x0, system: 0x0},
	109: {compactTag: 0x29d, symIndex: 0x22, system: 0x0},
	110: {compactTag: 0x29d, symIndex: 0x40, system: 0x3},
	111: {compactTag: 0x29d, symIndex: 0x48, system: 0x4},
	112: {compactTag: 0x29d, symIndex: 0x4d, system: 0xc},
	113: {compactTag: 0x2bd, symIndex: 0x31, system: 0x0},
	114: {compactTag: 0x2bd, symIndex: 0x3e, system: 0x3},
	115: {compactTag: 0x2bd, symIndex: 0x42, system: 0x4},
	116: {compactTag: 0x2cd, symIndex: 0x1b, system: 0x0},
	117: {compactTag: 0x2cd, symIndex: 0x49, system: 0x4},
	118: {compactTag: 0x2ce, symIndex: 0x49, system: 0x4},
	119: {compactTag: 0x2d0, symIndex: 0x33, system: 0x0},
	120: {compactTag: 0x2d0, symIndex: 0x4a, system: 0x4},
	121: {compactTag: 0x2d1, symIndex: 0x42, system: 0x4},
	122: {compactTag: 0x2d1, symIndex: 0x28, system: 0x0},
	123: {compactTag: 0x2d3, symIndex: 0x34, system: 0x0},
	124: {compactTag: 0x2d3, symIndex: 0x4b, system: 0x4},
	125: {compactTag: 0x2f9, symIndex: 0x0, system: 0x0},
	126: {compactTag: 0x2f9, symIndex: 0x38, system: 0x3},
	127: {compactTag: 0x2f9, symIndex: 0x42, system: 0x4},
	128: {compactTag: 0x2ff, symIndex: 0x36, system: 0x0},
	129: {compactTag: 0x2ff, symIndex: 0x41, system: 0x3},
	130: {compactTag: 0x2ff, symIndex: 0x4c, system: 0x4},
} // Size: 810 bytes

var tagToDecimal = []uint8{ // 775 elements
	// Entry 0 - 3F
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x05, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 40 - 7F
	0x05, 0x05, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x05, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x05, 0x05, 0x05, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x05, 0x05, 0x01, 0x01,
	// Entry 80 - BF
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry C0 - FF
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 100 - 13F
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 140 - 17F
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x05, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x05,
	0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 180 - 1BF
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x05, 0x05, 0x05, 0x05,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 1C0 - 1FF
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x05, 0x05,
	0x01, 0x01, 0x01, 0x05, 0x05, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 200 - 23F
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x05, 0x05, 0x01, 0x01, 0x01, 0x05, 0x01,
	0x01, 0x05, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 240 - 27F
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 280 - 2BF
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x05,
	0x05, 0x05, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 2C0 - 2FF
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x01,
	// Entry 300 - 33F
	0x01, 0x01, 0x01, 0x01, 0x01, 0x01, 0x08,
} // Size: 799 bytes

var tagToScientific = []uint8{ // 775 elements
	// Entry 0 - 3F
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 40 - 7F
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 80 - BF
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry C0 - FF
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 100 - 13F
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 140 - 17F
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x0c, 0x0c, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x0c,
	0x0c, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 180 - 1BF
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 1C0 - 1FF
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x0d, 0x0d, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x0c, 0x0c, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 200 - 23F
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x0c, 0x02,
	0x02, 0x0c, 0x0c, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 240 - 27F
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x0d, 0x0d, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 280 - 2BF
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 2C0 - 2FF
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x02,
	// Entry 300 - 33F
	0x02, 0x02, 0x02, 0x02, 0x02, 0x02, 0x09,
} // Size: 799 bytes

var tagToPercent = []uint8{ // 775 elements
	// Entry 0 - 3F
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x06, 0x06, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x03, 0x03, 0x03, 0x03, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	// Entry 40 - 7F
	0x06, 0x06, 0x06, 0x04, 0x04, 0x04, 0x03, 0x03,
	0x06, 0x06, 0x03, 0x04, 0x04, 0x03, 0x03, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x06, 0x06, 0x06, 0x03,
	0x03, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x03, 0x03,
	0x03, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04, 0x03,
	0x03, 0x04, 0x03, 0x04, 0x04, 0x03, 0x03, 0x03,
	0x03, 0x04, 0x04, 0x04, 0x07, 0x07, 0x04, 0x04,
	// Entry 80 - BF
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x03, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x03, 0x04, 0x03, 0x04,
	0x04, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x06, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	// Entry C0 - FF
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x03, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	// Entry 100 - 13F
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x04, 0x04,
	0x0b, 0x0b, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x03, 0x03, 0x04, 0x04,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x04, 0x03, 0x03,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	// Entry 140 - 17F
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x06, 0x06, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x06,
	0x06, 0x04, 0x04, 0x04, 0x03, 0x03, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	// Entry 180 - 1BF
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x06, 0x06, 0x06, 0x06,
	0x04, 0x04, 0x04, 0x04, 0x03, 0x03, 0x04, 0x04,
	// Entry 1C0 - 1FF
	0x04, 0x04, 0x04, 0x04, 0x04, 0x03, 0x03, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x03, 0x03, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	// Entry 200 - 23F
	0x04, 0x04, 0x04, 0x04, 0x03, 0x03, 0x03, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x06, 0x06, 0x04, 0x04, 0x04, 0x06, 0x04,
	0x04, 0x06, 0x06, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	// Entry 240 - 27F
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x03,
	0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x04, 0x04, 0x03, 0x03, 0x03, 0x03,
	0x03, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x03, 0x03, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x03, 0x03, 0x03, 0x03, 0x04, 0x04,
	// Entry 280 - 2BF
	0x04, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x03, 0x03, 0x03,
	0x03, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x06,
	0x06, 0x06, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x03, 0x03, 0x04, 0x04, 0x04, 0x04, 0x0e,
	// Entry 2C0 - 2FF
	0x0e, 0x0e, 0x04, 0x03, 0x03, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x03,
	0x03, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x04,
	// Entry 300 - 33F
	0x04, 0x04, 0x04, 0x04, 0x04, 0x04, 0x0a,
} // Size: 799 bytes

var formats = []Pattern{Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
	MaxFractionDigits:    0,
	Increment:            0x0,
	IncrementScale:       0x0,
	Mode:                 0x0,
	DigitShift:           0x0,
	MinIntegerDigits:     0x0,
	MaxIntegerDigits:     0x0,
	MinFractionDigits:    0x0,
	MinSignificantDigits: 0x0,
	MinExponentDigits:    0x0},
	Affix:       "",
	Offset:      0x0,
	NegOffset:   0x0,
	PadRune:     0,
	FormatWidth: 0x0,
	GroupingSize: [2]uint8{0x0,
		0x0},
	Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    3,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x0,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x9,
		GroupingSize: [2]uint8{0x3,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x0,
		MinIntegerDigits:     0x0,
		MaxIntegerDigits:     0x1,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x1},
		Affix:       "",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x3,
		GroupingSize: [2]uint8{0x0,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x2,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "\x00\x03\u00a0%",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x7,
		GroupingSize: [2]uint8{0x3,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x2,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "\x00\x01%",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x6,
		GroupingSize: [2]uint8{0x3,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    3,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x0,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0xc,
		GroupingSize: [2]uint8{0x3,
			0x2},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x2,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "\x00\x01%",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x9,
		GroupingSize: [2]uint8{0x3,
			0x2},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x2,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "\x00\x03\u00a0%",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0xa,
		GroupingSize: [2]uint8{0x3,
			0x2},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    6,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x0,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x8,
		GroupingSize: [2]uint8{0x0,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    6,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x0,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x6,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x3},
		Affix:       "",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0xd,
		GroupingSize: [2]uint8{0x0,
			0x0},
		Flags: 0x4},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x2,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "\x00\x01%",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x2,
		GroupingSize: [2]uint8{0x0,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x2,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "\x03%\u00a0\x00",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x7,
		GroupingSize: [2]uint8{0x3,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x0,
		MinIntegerDigits:     0x0,
		MaxIntegerDigits:     0x1,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x1},
		Affix:       "\x01[\x01]",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x5,
		GroupingSize: [2]uint8{0x0,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x0,
		MinIntegerDigits:     0x0,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x1,
		GroupingSize: [2]uint8{0x0,
			0x0},
		Flags: 0x0},
	Pattern{RoundingContext: RoundingContext{MaxSignificantDigits: 0,
		MaxFractionDigits:    0,
		Increment:            0x0,
		IncrementScale:       0x0,
		Mode:                 0x0,
		DigitShift:           0x2,
		MinIntegerDigits:     0x1,
		MaxIntegerDigits:     0x0,
		MinFractionDigits:    0x0,
		MinSignificantDigits: 0x0,
		MinExponentDigits:    0x0},
		Affix:       "\x01%\x00",
		Offset:      0x0,
		NegOffset:   0x0,
		PadRune:     0,
		FormatWidth: 0x6,
		GroupingSize: [2]uint8{0x3,
			0x0},
		Flags: 0x0}}

// Total table size 8634 bytes (8KiB); checksum: 8F23386D
